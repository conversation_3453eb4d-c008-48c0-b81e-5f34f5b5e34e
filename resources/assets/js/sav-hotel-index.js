/**
 * Hotel Management System - Index Page
 * 
 * This file handles the functionality for the hotel index page, including:
 * - Initialization of select2 dropdowns for filtering (countries, regions, subregions, cities, price ranges, statuses)
 * - AJAX requests to fetch filtered data for the select dropdowns
 * - DataTable initialization for displaying hotel data
 * - Filter button functionality to filter hotels based on selected criteria
 * - Map view button functionality to view hotels on a map
 * - Error handling for AJAX requests
 * 
 * Dependencies:
 * - jQuery
 * - DataTables
 * - Select2
 * - SweetAlert2
 * - FormValidation
 * - Cleave.js
 */

'use strict';

// Datatable (jquery)
$(function () {

  console.log('Start Adrian!!!');

  // Variable declaration for table
  var dt_user_table = $('.datatables-users'),
    dt_hotel_table = $('.datatables-teams'),
    select2 = $('.select2'),
    select2Status = $('.select2-status'),
    select2MultipleCountry = $('.select2-country'),
    select2MultipleLevel1 = $('.select2-level1'),
    select2MultipleLevel2 = $('.select2-level2'),
    select2MultipleCity = $('.select2-city'),
    select2MultiplePrice = $('.select2-price'),
    userView = baseUrl + 'app/user/view/account',
    offCanvasForm = $('#offcanvasAddUser');

  if (select2.length) {
    var $this = select2;
    $this.wrap('<div class="position-relative"></div>').select2({
      placeholder: 'Select Country',
      dropdownParent: $this.parent()
    });
  }

  if (select2Status.length) {
    var $this = select2Status;
    $this.wrap('<div class="position-relative"></div>').select2({
      placeholder: 'Alege status',
      dropdownParent: $this.parent()
    });
  }

  if (select2MultipleCountry.length) {
    var $this = select2MultipleCountry;
    $this.wrap('<div class="position-relative"></div>').select2({
      placeholder: 'Alege țara',
      dropdownParent: $this.parent()
    });
  }

  if (select2MultipleLevel1.length) {
    var $this = select2MultipleLevel1;
    $this.wrap('<div class="position-relative"></div>').select2({
      placeholder: 'Alege regiunea',
      dropdownParent: $this.parent()
    });
  }

  if (select2MultipleLevel2.length) {
    var $this = select2MultipleLevel2;
    $this.wrap('<div class="position-relative"></div>').select2({
      placeholder: 'Alege sub-regiunea',
      dropdownParent: $this.parent(),
      closeOnSelect: false
    });
  }

  if (select2MultipleCity.length) {
    var $this = select2MultipleCity;
    $this.wrap('<div class="position-relative"></div>').select2({
      placeholder: 'Tastează minim 3 caractere pentru a căuta orașe',
      dropdownParent: $this.parent(),
      closeOnSelect: false,
      minimumInputLength: 3,
      ajax: {
        url: window.routes.hotelApiSearchCitiesUrl,
        dataType: 'json',
        delay: 250,
        data: function (params) {
          return {
            search: params.term
          };
        },
        processResults: function (data) {
          let results = [];

          // Process the grouped data from the API
          if (data.countries && data.countries.length > 0) {
            data.countries.forEach(function(country) {
              country.regions.forEach(function(region) {
                let group = {
                  text: country.country + ' - ' + region.region,
                  children: []
                };

                region.cities.forEach(function(city) {
                  group.children.push({
                    id: city.id,
                    text: city.name
                  });
                });

                results.push(group);
              });
            });
          }

          return {
            results: results
          };
        },
        cache: true
      }
    });
  }

  if (select2MultiplePrice.length) {
    var $this = select2MultiplePrice;
    $this.wrap('<div class="position-relative"></div>').select2({
      placeholder: 'Select price range',
      dropdownParent: $this.parent(),
      closeOnSelect: false
    });
  }

  // ajax setup
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });

  // Hotels datatable
  if (dt_hotel_table.length) {
    var dt_hotel = dt_hotel_table.DataTable({
      processing: true,
      serverSide: true,
      ajax: {
        url: window.routes.hotelApiFetchHotelsUrl
      },
      columns: [
        // columns according to JSON
        { data: '' },
        { data: 'id' },
        { data: 'name' },
        { data: 'city' },
        { data: 'country' },
        { data: 'region' },
        { data: 'subregion' },
        { data: 'price' },
        { data: 'status' },
        { data: 'actions' }
      ],
      columnDefs: [
        {
          // For Responsive
          className: 'control',
          searchable: false,
          orderable: false,
          responsivePriority: 2,
          targets: 0,
          render: function (data, type, full, meta) {
            return '';
          }
        },
        {
          // ID column
          searchable: true,
          orderable: true,
          targets: 1
        },
        {
          // Hotel name column
          targets: 2,
          responsivePriority: 4,
          render: function (data, type, full, meta) {
            var $name = full['name'];

            // For Avatar badge
            var stateNum = Math.floor(Math.random() * 6);
            var states = ['success', 'danger', 'warning', 'info', 'dark', 'primary', 'secondary'];
            var $state = states[stateNum],
              $initials = $name.match(/\b\w/g) || [],
              $output;
            $initials = (($initials.shift() || '') + ($initials.pop() || '')).toUpperCase();
            $output = '<span class="avatar-initial rounded-circle bg-label-' + $state + '">' + $initials + '</span>';

            // Creates full output for row
            var $row_output =
              '<div class="d-flex justify-content-start align-items-center hotel-name">' +
              '<div class="avatar-wrapper">' +
              '<div class="avatar avatar-sm me-4">' +
              $output +
              '</div>' +
              '</div>' +
              '<div class="d-flex flex-column">' +
              '<span class="fw-medium">' + $name + '</span>' +
              '</div>' +
              '</div>';
            return $row_output;
          }
        },
        {
          // City column
          targets: 3,
          render: function (data, type, full, meta) {
            return '<span>' + full['city'] + '</span>';
          }
        },
        {
          // Country column
          targets: 4,
          render: function (data, type, full, meta) {
            return '<span>' + full['country'] + '</span>';
          }
        },
        {
          // Region column
          targets: 5,
          render: function (data, type, full, meta) {
            return '<span>' + full['region'] + '</span>';
          }
        },
        {
          // Subregion column
          targets: 6,
          render: function (data, type, full, meta) {
            return '<span>' + full['subregion'] + '</span>';
          }
        },
        {
          // Price column
          targets: 7,
          render: function (data, type, full, meta) {
            return '<span>' + full['price'] + '</span>';
          }
        },
        {
          // Status column
          targets: 8,
          render: function (data, type, full, meta) {
            var $status_number = full['status'];
            var $status = {
              1: { title: 'Active', class: 'bg-label-success' },
              2: { title: 'Inactive', class: 'bg-label-secondary' },
              3: { title: 'Suspended', class: 'bg-label-danger' },
              4: { title: 'Pending', class: 'bg-label-warning' },
              5: { title: 'Partner', class: 'bg-label-info' }
            };
            if (typeof $status[$status_number] === 'undefined') {
              return data;
            }
            return (
              '<span class="badge ' + $status[$status_number].class + '">' + $status[$status_number].title + '</span>'
            );
          }
        },
        {
          // Actions column
          targets: -1,
          title: 'Actions',
          searchable: false,
          orderable: false,
          render: function (data, type, full, meta) {
            return (
              '<div class="d-flex align-items-center gap-50">' +
              `<button class="btn btn-sm btn-icon edit-hotel btn-text-secondary rounded-pill waves-effect" data-id="${full['id']}" data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddTeam"><i class="ti ti-edit"></i></button>` +
              `<button class="btn btn-sm btn-icon delete-hotel btn-text-secondary rounded-pill waves-effect" data-id="${full['id']}"><i class="ti ti-trash"></i></button>` +
              '<button class="btn btn-sm btn-icon btn-text-secondary rounded-pill waves-effect dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical"></i></button>' +
              '<div class="dropdown-menu dropdown-menu-end m-0">' +
              '<a href="javascript:;" class="dropdown-item">View Details</a>' +
              '<a href="javascript:;" class="dropdown-item">Edit</a>' +
              '</div>' +
              '</div>'
            );
          }
        }
      ],
      order: [[2, 'asc']],
      dom:
        '<"row"' +
        '<"col-md-2"<"ms-n2"l>>' +
        '<"col-md-10"<"dt-action-buttons text-xl-end text-lg-start text-md-end text-start d-flex align-items-center justify-content-end flex-md-row flex-column mb-6 mb-md-0 mt-n6 mt-md-0"fB>>' +
        '>t' +
        '<"row"' +
        '<"col-sm-12 col-md-6"i>' +
        '<"col-sm-12 col-md-6"p>' +
        '>',
      lengthMenu: [7, 10, 25, 50, 75, 100],
      language: {
        sLengthMenu: '_MENU_',
        search: '',
        searchPlaceholder: 'Search Hotel',
        info: 'Displaying _START_ to _END_ of _TOTAL_ entries',
        paginate: {
          next: '<i class="ti ti-chevron-right ti-sm"></i>',
          previous: '<i class="ti ti-chevron-left ti-sm"></i>'
        }
      },
      // Buttons with Dropdown
      buttons: [
        {
          extend: 'collection',
          className: 'btn btn-label-secondary dropdown-toggle mx-4 waves-effect waves-light',
          text: '<i class="ti ti-upload me-2 ti-xs"></i>Export',
          buttons: [
            {
              extend: 'print',
              title: 'Hotels',
              text: '<i class="ti ti-printer me-2" ></i>Print',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7, 8],
                // prevent avatar to be print
                format: {
                  body: function (inner, coldex, rowdex) {
                    if (inner.length <= 0) return inner;
                    var el = $.parseHTML(inner);
                    var result = '';
                    $.each(el, function (index, item) {
                      if (item.classList !== undefined && item.classList.contains('hotel-name')) {
                        result = result + item.lastChild.firstChild.textContent;
                      } else if (item.innerText === undefined) {
                        result = result + item.textContent;
                      } else result = result + item.innerText;
                    });
                    return result;
                  }
                }
              },
              customize: function (win) {
                //customize print view for dark
                $(win.document.body)
                  .css('color', config.colors.headingColor)
                  .css('border-color', config.colors.borderColor)
                  .css('background-color', config.colors.body);
                $(win.document.body)
                  .find('table')
                  .addClass('compact')
                  .css('color', 'inherit')
                  .css('border-color', 'inherit')
                  .css('background-color', 'inherit');
              }
            },
            {
              extend: 'csv',
              title: 'Hotels',
              text: '<i class="ti ti-file-text me-2" ></i>Csv',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7, 8],
                // prevent avatar to be display
                format: {
                  body: function (inner, coldex, rowdex) {
                    if (inner.length <= 0) return inner;
                    var el = $.parseHTML(inner);
                    var result = '';
                    $.each(el, function (index, item) {
                      if (item.classList !== undefined && item.classList.contains('hotel-name')) {
                        result = result + item.lastChild.firstChild.textContent;
                      } else if (item.innerText === undefined) {
                        result = result + item.textContent;
                      } else result = result + item.innerText;
                    });
                    return result;
                  }
                }
              }
            },
            {
              extend: 'excel',
              title: 'Hotels',
              text: '<i class="ti ti-file-spreadsheet me-2"></i>Excel',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7, 8],
                // prevent avatar to be display
                format: {
                  body: function (inner, coldex, rowdex) {
                    if (inner.length <= 0) return inner;
                    var el = $.parseHTML(inner);
                    var result = '';
                    $.each(el, function (index, item) {
                      if (item.classList !== undefined && item.classList.contains('hotel-name')) {
                        result = result + item.lastChild.firstChild.textContent;
                      } else if (item.innerText === undefined) {
                        result = result + item.textContent;
                      } else result = result + item.innerText;
                    });
                    return result;
                  }
                }
              }
            },
            {
              extend: 'pdf',
              title: 'Hotels',
              text: '<i class="ti ti-file-code-2 me-2"></i>Pdf',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7, 8],
                // prevent avatar to be display
                format: {
                  body: function (inner, coldex, rowdex) {
                    if (inner.length <= 0) return inner;
                    var el = $.parseHTML(inner);
                    var result = '';
                    $.each(el, function (index, item) {
                      if (item.classList !== undefined && item.classList.contains('hotel-name')) {
                        result = result + item.lastChild.firstChild.textContent;
                      } else if (item.innerText === undefined) {
                        result = result + item.textContent;
                      } else result = result + item.innerText;
                    });
                    return result;
                  }
                }
              }
            },
            {
              extend: 'copy',
              title: 'Hotels',
              text: '<i class="ti ti-copy me-2" ></i>Copy',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5, 6, 7, 8],
                // prevent avatar to be display
                format: {
                  body: function (inner, coldex, rowdex) {
                    if (inner.length <= 0) return inner;
                    var el = $.parseHTML(inner);
                    var result = '';
                    $.each(el, function (index, item) {
                      if (item.classList !== undefined && item.classList.contains('hotel-name')) {
                        result = result + item.lastChild.firstChild.textContent;
                      } else if (item.innerText === undefined) {
                        result = result + item.textContent;
                      } else result = result + item.innerText;
                    });
                    return result;
                  }
                }
              }
            }
          ]
        },
        {
          text: '<i class="ti ti-plus me-0 me-sm-1 ti-xs"></i><span class="d-none d-sm-inline-block">Add New Hotel</span>',
          className: 'add-new btn btn-primary waves-effect waves-light',
          attr: {
            'data-bs-toggle': 'offcanvas',
            'data-bs-target': '#offcanvasAddTeam'
          }
        }
      ],
      // For responsive popup
      responsive: {
        details: {
          display: $.fn.dataTable.Responsive.display.modal({
            header: function (row) {
              var data = row.data();
              return 'Details of ' + data['name'];
            }
          }),
          type: 'column',
          renderer: function (api, rowIdx, columns) {
            var data = $.map(columns, function (col, i) {
              return col.title !== '' // ? Do not show row in modal popup if title is blank (for check box)
                ? '<tr data-dt-row="' +
                    col.rowIndex +
                    '" data-dt-column="' +
                    col.columnIndex +
                    '">' +
                    '<td>' +
                    col.title +
                    ':' +
                    '</td> ' +
                    '<td>' +
                    col.data +
                    '</td>' +
                    '</tr>'
                : '';
            }).join('');

            return data ? $('<table class="table"/><tbody />').append(data) : false;
          }
        }
      }
    });
  }

  // Users datatable
  if (dt_user_table.length) {
    var dt_user = dt_user_table.DataTable({
      processing: true,
      serverSide: true,
      ajax: {
        url: window.routes.hotelApiFetchHotelsUrl
      },
      columns: [
        // columns according to JSON
        { data: '' },
        { data: 'id' },
        { data: 'name' },
        { data: 'city' },
        { data: 'country' },
        { data: 'region' },
        { data: 'subregion' },
        { data: 'price' },
        { data: 'status' },
        { data: 'action' }
      ],
      columnDefs: [
        {
          // For Responsive
          className: 'control',
          searchable: false,
          orderable: false,
          responsivePriority: 2,
          targets: 0,
          render: function (data, type, full, meta) {
            return '';
          }
        },
        {
          searchable: false,
          orderable: false,
          targets: 1,
          render: function (data, type, full, meta) {
            return `<span>${full.fake_id}</span>`;
          }
        },
        {
          // User full name
          targets: 2,
          responsivePriority: 4,
          render: function (data, type, full, meta) {
            var $name = full['name'];

            // For Avatar badge
            var stateNum = Math.floor(Math.random() * 6);
            var states = ['success', 'danger', 'warning', 'info', 'dark', 'primary', 'secondary'];
            var $state = states[stateNum],
              $name = full['name'],
              $initials = $name.match(/\b\w/g) || [],
              $output;
            $initials = (($initials.shift() || '') + ($initials.pop() || '')).toUpperCase();
            $output = '<span class="avatar-initial rounded-circle bg-label-' + $state + '">' + $initials + '</span>';

            // Creates full output for row
            var $row_output =
              '<div class="d-flex justify-content-start align-items-center user-name">' +
              '<div class="avatar-wrapper">' +
              '<div class="avatar avatar-sm me-4">' +
              $output +
              '</div>' +
              '</div>' +
              '<div class="d-flex flex-column">' +
              '<a href="' +
              userView +
              '" class="text-heading text-truncate"><span class="fw-medium">' +
              $name +
              '</span></a>' +
              '</div>' +
              '</div>';
            return $row_output;
          }
        },
        {
          // User email
          targets: 3,
          render: function (data, type, full, meta) {
            var $email = full['email'];

            return '<span class="user-email">' + $email + '</span>';
          }
        },
        {
          // email verify
          targets: 4,
          className: 'text-center',
          render: function (data, type, full, meta) {
            var $verified = full['email_verified_at'];
            return `${
              $verified
                ? '<i class="ti fs-4 ti-shield-check text-success"></i>'
                : '<i class="ti fs-4 ti-shield-x text-danger" ></i>'
            }`;
          }
        },
        {
          // Actions
          targets: -1,
          title: 'Actions',
          searchable: false,
          orderable: false,
          render: function (data, type, full, meta) {
            return (
              '<div class="d-flex align-items-center gap-50">' +
              `<button class="btn btn-sm btn-icon edit-record btn-text-secondary rounded-pill waves-effect" data-id="${full['id']}" data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddUser"><i class="ti ti-edit"></i></button>` +
              `<button class="btn btn-sm btn-icon delete-record btn-text-secondary rounded-pill waves-effect" data-id="${full['id']}"><i class="ti ti-trash"></i></button>` +
              '<button class="btn btn-sm btn-icon btn-text-secondary rounded-pill waves-effect dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical"></i></button>' +
              '<div class="dropdown-menu dropdown-menu-end m-0">' +
              '<a href="' +
              userView +
              '" class="dropdown-item">View</a>' +
              '<a href="javascript:;" class="dropdown-item">Suspend</a>' +
              '</div>' +
              '</div>'
            );
          }
        }
      ],
      order: [[2, 'desc']],
      dom:
        '<"row"' +
        '<"col-md-2"<"ms-n2"l>>' +
        '<"col-md-10"<"dt-action-buttons text-xl-end text-lg-start text-md-end text-start d-flex align-items-center justify-content-end flex-md-row flex-column mb-6 mb-md-0 mt-n6 mt-md-0"fB>>' +
        '>t' +
        '<"row"' +
        '<"col-sm-12 col-md-6"i>' +
        '<"col-sm-12 col-md-6"p>' +
        '>',
      lengthMenu: [7, 10, 20, 50, 70, 100], //for length of menu
      language: {
        sLengthMenu: '_MENU_',
        search: '',
        searchPlaceholder: 'Search User',
        info: 'Displaying _START_ to _END_ of _TOTAL_ entries',
        paginate: {
          next: '<i class="ti ti-chevron-right ti-sm"></i>',
          previous: '<i class="ti ti-chevron-left ti-sm"></i>'
        }
      },
      // Buttons with Dropdown
      buttons: [
        {
          extend: 'collection',
          className: 'btn btn-label-secondary dropdown-toggle mx-4 waves-effect waves-light',
          text: '<i class="ti ti-upload me-2 ti-xs"></i>Export',
          buttons: [
            {
              extend: 'print',
              title: 'Users',
              text: '<i class="ti ti-printer me-2" ></i>Print',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5],
                // prevent avatar to be print
                format: {
                  body: function (inner, coldex, rowdex) {
                    if (inner.length <= 0) return inner;
                    var el = $.parseHTML(inner);
                    var result = '';
                    $.each(el, function (index, item) {
                      if (item.classList !== undefined && item.classList.contains('user-name')) {
                        result = result + item.lastChild.firstChild.textContent;
                      } else if (item.innerText === undefined) {
                        result = result + item.textContent;
                      } else result = result + item.innerText;
                    });
                    return result;
                  }
                }
              },
              customize: function (win) {
                //customize print view for dark
                $(win.document.body)
                  .css('color', config.colors.headingColor)
                  .css('border-color', config.colors.borderColor)
                  .css('background-color', config.colors.body);
                $(win.document.body)
                  .find('table')
                  .addClass('compact')
                  .css('color', 'inherit')
                  .css('border-color', 'inherit')
                  .css('background-color', 'inherit');
              }
            },
            {
              extend: 'csv',
              title: 'Users',
              text: '<i class="ti ti-file-text me-2" ></i>Csv',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5],
                // prevent avatar to be display
                format: {
                  body: function (inner, coldex, rowdex) {
                    if (inner.length <= 0) return inner;
                    var el = $.parseHTML(inner);
                    var result = '';
                    $.each(el, function (index, item) {
                      if (item.classList !== undefined && item.classList.contains('user-name')) {
                        result = result + item.lastChild.firstChild.textContent;
                      } else if (item.innerText === undefined) {
                        result = result + item.textContent;
                      } else result = result + item.innerText;
                    });
                    return result;
                  }
                }
              }
            },
            {
              extend: 'excel',
              title: 'Users',
              text: '<i class="ti ti-file-spreadsheet me-2"></i>Excel',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5],
                // prevent avatar to be display
                format: {
                  body: function (inner, coldex, rowdex) {
                    if (inner.length <= 0) return inner;
                    var el = $.parseHTML(inner);
                    var result = '';
                    $.each(el, function (index, item) {
                      if (item.classList !== undefined && item.classList.contains('user-name')) {
                        result = result + item.lastChild.firstChild.textContent;
                      } else if (item.innerText === undefined) {
                        result = result + item.textContent;
                      } else result = result + item.innerText;
                    });
                    return result;
                  }
                }
              }
            },
            {
              extend: 'pdf',
              title: 'Users',
              text: '<i class="ti ti-file-code-2 me-2"></i>Pdf',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5],
                // prevent avatar to be display
                format: {
                  body: function (inner, coldex, rowdex) {
                    if (inner.length <= 0) return inner;
                    var el = $.parseHTML(inner);
                    var result = '';
                    $.each(el, function (index, item) {
                      if (item.classList !== undefined && item.classList.contains('user-name')) {
                        result = result + item.lastChild.firstChild.textContent;
                      } else if (item.innerText === undefined) {
                        result = result + item.textContent;
                      } else result = result + item.innerText;
                    });
                    return result;
                  }
                }
              }
            },
            {
              extend: 'copy',
              title: 'Users',
              text: '<i class="ti ti-copy me-2" ></i>Copy',
              className: 'dropdown-item',
              exportOptions: {
                columns: [1, 2, 3, 4, 5],
                // prevent avatar to be display
                format: {
                  body: function (inner, coldex, rowdex) {
                    if (inner.length <= 0) return inner;
                    var el = $.parseHTML(inner);
                    var result = '';
                    $.each(el, function (index, item) {
                      if (item.classList !== undefined && item.classList.contains('user-name')) {
                        result = result + item.lastChild.firstChild.textContent;
                      } else if (item.innerText === undefined) {
                        result = result + item.textContent;
                      } else result = result + item.innerText;
                    });
                    return result;
                  }
                }
              }
            }
          ]
        },
        {
          text: '<i class="ti ti-plus me-0 me-sm-1 ti-xs"></i><span class="d-none d-sm-inline-block">Add New User</span>',
          className: 'add-new btn btn-primary waves-effect waves-light',
          attr: {
            'data-bs-toggle': 'offcanvas',
            'data-bs-target': '#offcanvasAddUser'
          }
        }
      ],
      // For responsive popup
      responsive: {
        details: {
          display: $.fn.dataTable.Responsive.display.modal({
            header: function (row) {
              var data = row.data();
              return 'Details of ' + data['name'];
            }
          }),
          type: 'column',
          renderer: function (api, rowIdx, columns) {
            var data = $.map(columns, function (col, i) {
              return col.title !== '' // ? Do not show row in modal popup if title is blank (for check box)
                ? '<tr data-dt-row="' +
                    col.rowIndex +
                    '" data-dt-column="' +
                    col.columnIndex +
                    '">' +
                    '<td>' +
                    col.title +
                    ':' +
                    '</td> ' +
                    '<td>' +
                    col.data +
                    '</td>' +
                    '</tr>'
                : '';
            }).join('');

            return data ? $('<table class="table"/><tbody />').append(data) : false;
          }
        }
      }
    });
  }

  // Delete Hotel Record
  $(document).on('click', '.delete-hotel', function () {
    var hotel_id = $(this).data('id'),
      dtrModal = $('.dtr-bs-modal.show');

    // hide responsive modal in small screen
    if (dtrModal.length) {
      dtrModal.modal('hide');
    }

    // sweetalert for confirmation of delete
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      customClass: {
        confirmButton: 'btn btn-primary me-3',
        cancelButton: 'btn btn-label-secondary'
      },
      buttonsStyling: false
    }).then(function (result) {
      if (result.value) {
        // delete the data
        $.ajax({
          type: 'DELETE',
          url: `${baseUrl}hotels/${hotel_id}`,
          success: function () {
            dt_hotel.draw();
          },
          error: function (error) {
            console.log(error);
          }
        });

        // success sweetalert
        Swal.fire({
          icon: 'success',
          title: 'Deleted!',
          text: 'The hotel has been deleted!',
          customClass: {
            confirmButton: 'btn btn-success'
          }
        });
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        Swal.fire({
          title: 'Cancelled',
          text: 'The hotel is not deleted!',
          icon: 'error',
          customClass: {
            confirmButton: 'btn btn-success'
          }
        });
      }
    });
  });

  // Edit Hotel Record
  $(document).on('click', '.edit-hotel', function () {
    var hotel_id = $(this).data('id'),
      dtrModal = $('.dtr-bs-modal.show');

    // hide responsive modal in small screen
    if (dtrModal.length) {
      dtrModal.modal('hide');
    }

    // changing the title of offcanvas
    $('#offcanvasAddTeamLabel').html('Edit Hotel');

    // get data
    $.get(`${baseUrl}hotels/${hotel_id}/edit`, function (data) {
      $('#team_id').val(data.id);
      $('#add-team-name').val(data.name);
      // Add more fields as needed
    });
  });

  // Delete User Record
  $(document).on('click', '.delete-record', function () {
    var user_id = $(this).data('id'),
      dtrModal = $('.dtr-bs-modal.show');

    // hide responsive modal in small screen
    if (dtrModal.length) {
      dtrModal.modal('hide');
    }

    // sweetalert for confirmation of delete
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      customClass: {
        confirmButton: 'btn btn-primary me-3',
        cancelButton: 'btn btn-label-secondary'
      },
      buttonsStyling: false
    }).then(function (result) {
      if (result.value) {
        // delete the data
        $.ajax({
          type: 'DELETE',
          url: `${baseUrl}user-list/${user_id}`,
          success: function () {
            dt_user.draw();
          },
          error: function (error) {
            console.log(error);
          }
        });

        // success sweetalert
        Swal.fire({
          icon: 'success',
          title: 'Deleted!',
          text: 'The user has been deleted!',
          customClass: {
            confirmButton: 'btn btn-success'
          }
        });
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        Swal.fire({
          title: 'Cancelled',
          text: 'The User is not deleted!',
          icon: 'error',
          customClass: {
            confirmButton: 'btn btn-success'
          }
        });
      }
    });
  });

  // edit record
  $(document).on('click', '.edit-record', function () {
    var user_id = $(this).data('id'),
      dtrModal = $('.dtr-bs-modal.show');

    // hide responsive modal in small screen
    if (dtrModal.length) {
      dtrModal.modal('hide');
    }

    // changing the title of offcanvas
    $('#offcanvasAddUserLabel').html('Edit User');

    // get data
    $.get(`${baseUrl}user-list\/${user_id}\/edit`, function (data) {
      $('#user_id').val(data.id);
      $('#add-user-fullname').val(data.name);
      $('#add-user-email').val(data.email);
    });
  });

  // changing the title
  $('.add-new').on('click', function () {
    $('#user_id').val(''); //reseting input field
    $('#offcanvasAddUserLabel').html('Add User');
  });

  // Filter form control to default size
  // ? setTimeout used for multilingual table initialization
  setTimeout(() => {
    $('.dataTables_filter .form-control').removeClass('form-control-sm');
    $('.dataTables_length .form-select').removeClass('form-select-sm');
  }, 300);

  // validating form and updating user's data
  const addNewUserForm = document.getElementById('addNewUserForm');

  // user form validation
  if (addNewUserForm) {
    const fv = FormValidation.formValidation(addNewUserForm, {
      fields: {
        name: {
          validators: {
            notEmpty: {
              message: 'Please enter fullname'
            }
          }
        },
        email: {
          validators: {
            notEmpty: {
              message: 'Please enter your email'
            },
            emailAddress: {
              message: 'The value is not a valid email address'
            }
          }
        },
        userContact: {
          validators: {
            notEmpty: {
              message: 'Please enter your contact'
            }
          }
        },
        company: {
          validators: {
            notEmpty: {
              message: 'Please enter your company'
            }
          }
        }
      },
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          // Use this for enabling/changing valid/invalid class
          eleValidClass: '',
          rowSelector: function (field, ele) {
            // field is the field name & ele is the field element
            return '.mb-6';
          }
        }),
        submitButton: new FormValidation.plugins.SubmitButton(),
        // Submit the form when all fields are valid
        // defaultSubmit: new FormValidation.plugins.DefaultSubmit(),
        autoFocus: new FormValidation.plugins.AutoFocus()
      }
    }).on('core.form.valid', function () {
      // adding or updating user when form successfully validate
      $.ajax({
        data: $('#addNewUserForm').serialize(),
        url: `${baseUrl}user-list`,
        type: 'POST',
        success: function (status) {
          dt_user.draw();
          offCanvasForm.offcanvas('hide');

          // sweetalert
          Swal.fire({
            icon: 'success',
            title: `Successfully ${status}!`,
            text: `User ${status} Successfully.`,
            customClass: {
              confirmButton: 'btn btn-success'
            }
          });
        },
        error: function (err) {
          offCanvasForm.offcanvas('hide');
          Swal.fire({
            title: 'Duplicate Entry!',
            text: 'Your email should be unique.',
            icon: 'error',
            customClass: {
              confirmButton: 'btn btn-success'
            }
          });
        }
      });
    });

    // clearing form data when offcanvas hidden
    offCanvasForm.on('hidden.bs.offcanvas', function () {
      fv.resetForm(true);
    });
  }

  const phoneMaskList = document.querySelectorAll('.phone-mask');

  // Phone Number
  if (phoneMaskList) {
    phoneMaskList.forEach(function (phoneMask) {
      new Cleave(phoneMask, {
        phone: true,
        phoneRegionCode: 'US'
      });
    });
  }

  /**
   * Handle country selection change
   * When countries are selected, fetch and populate the regions dropdown
   */
  select2MultipleCountry.on('change', function (e) {
    const selectedValues = $(this).val();
    const url = window.routes.hotelApiFetchRegionsGroupedUrl;

    // Clear dependent dropdowns
    $('#select2MultipleLevel1').empty().trigger('change');
    $('#select2MultipleLevel2').empty().trigger('change');
    // We no longer clear the city dropdown as it's now independent and uses search

    // If no countries selected, don't make the AJAX request
    if (!selectedValues || selectedValues.length === 0) {
      return;
    }

    // Show loading indicator
    const $select = $('#select2MultipleLevel1');
    $select.append('<option value="" disabled selected>Loading regions...</option>').trigger('change');

    $.ajax({
        url: url,
        method: 'GET',
        data: { countries: selectedValues },
        success: function(response) {
          $select.empty(); // Clear previous options

          // If no regions found
          if (!response.countries || response.countries.length === 0) {
            $select.append('<option value="" disabled selected>No regions found</option>').trigger('change');
            return;
          }

          // Populate regions dropdown
          response.countries.forEach(country => {
            const $optgroup = $('<optgroup>', { label: country.country });

            country.regions.forEach(region => {
              const $option = $('<option>', {
                value: region.id,
                text: region.name
              });
              $optgroup.append($option);
            });

            $select.append($optgroup);
          });

          // Update Select2
          $select.trigger('change');
        },
        error: function(xhr, status, error) {
          console.error('Error fetching regions:', error);
          $select.empty().append('<option value="" disabled selected>Error loading regions</option>').trigger('change');

          // Show error notification
          Swal.fire({
            title: 'Error!',
            text: 'Failed to load regions. Please try again.',
            icon: 'error',
            customClass: {
              confirmButton: 'btn btn-primary'
            },
            buttonsStyling: false
          });
        }
    });
  });

  /**
   * Handle region selection change
   * When regions are selected, fetch and populate the subregions dropdown
   */
  select2MultipleLevel1.on('change', function (e) {
    const selectedValues = $(this).val();
    const url = window.routes.hotelApiFetchSubregionsGroupedUrl;

    // Clear dependent dropdowns
    $('#select2MultipleLevel2').empty().trigger('change');
    // We no longer clear the city dropdown as it's now independent and uses search

    // If no regions selected, don't make the AJAX request
    if (!selectedValues || selectedValues.length === 0) {
      return;
    }

    // Show loading indicator
    const $select = $('#select2MultipleLevel2');
    $select.append('<option value="" disabled selected>Loading subregions...</option>').trigger('change');

    $.ajax({
      url: url,
      method: 'GET',
      data: { regions: selectedValues },
      success: function(response) {
        $select.empty(); // Clear previous options

        // If no subregions found
        if (!response.regions || response.regions.length === 0) {
          $select.append('<option value="" disabled selected>No subregions found</option>').trigger('change');
          return;
        }

        // Populate subregions dropdown
        response.regions.forEach(region => {
          const $optgroup = $('<optgroup>', { label: region.region });

          region.subregions.forEach(subregion => {
            const $option = $('<option>', {
              value: subregion.id,
              text: subregion.name
            });
            $optgroup.append($option);
          });

          $select.append($optgroup);
        });

        // Update Select2
        $select.trigger('change');
      },
      error: function(xhr, status, error) {
        console.error('Error fetching subregions:', error);
        $select.empty().append('<option value="" disabled selected>Error loading subregions</option>').trigger('change');

        // Show error notification
        Swal.fire({
          title: 'Error!',
          text: 'Failed to load subregions. Please try again.',
          icon: 'error',
          customClass: {
            confirmButton: 'btn btn-primary'
          },
          buttonsStyling: false
        });
      }
    });
  });

  /**
   * Handle subregion selection change
   * Note: We no longer populate the cities dropdown based on subregion selection
   * Cities are now loaded via AJAX search when the user types at least 3 characters
   */
  select2MultipleLevel2.on('change', function (e) {
    // We don't need to do anything with the city dropdown anymore
    // It's now independent and uses the search API
  });




  /**
   * Handle "View on map" button click
   * Opens a map view with the filtered hotels
   */
  $('.btn-primary:contains("View on map")').on('click', function (e) {
    e.preventDefault();

    // Collect selected values from all multiple selects
    const params = new URLSearchParams();

    // Helper function to add parameters to query string
    function addParams(name, values) {
      if (values && values.length) {
        values.forEach(function (val) {
          params.append(name + '[]', val);
        });
      }
    }

    // Country
    const countries = $('#select2MultipleCountry').val() || [];
    addParams('country', countries);

    // Region (Level 1)
    const level1 = $('#select2MultipleLevel1').val() || [];
    addParams('region', level1);

    // Subregion (Level 2)
    const level2 = $('#select2MultipleLevel2').val() || [];
    addParams('subregion', level2);

    // City
    const cities = $('#select2MultipleCity').val() || [];
    addParams('city', cities);

    // Price range
    const prices = $('#select2MultiplePrice').val() || [];
    addParams('price', prices);

    // Status
    const statuses = $('#select2Status').val() || [];
    addParams('status', statuses);

    // Hotel name (text input)
    const hotelName = $('#hotel_name').val();
    if (hotelName) {
      params.append('hotel_name', hotelName);
    }

    // Redirect to map view with filter parameters
    const mapUrl = baseUrl + 'hotels/map?' + params.toString();
    window.open(mapUrl, '_blank');
  });

  /**
   * Handle filter button click
   * Collects all filter values and either:
   * 1. Reloads the page with filter parameters in URL
   * 2. Updates the DataTable with filtered data
   */
  $('.btn-filter').on('click', function (e) {
    e.preventDefault();

    // Collect selected values from all multiple selects
    const params = new URLSearchParams();

    // Helper function to add parameters to query string
    function addParams(name, values) {
      if (values && values.length) {
        values.forEach(function (val) {
          params.append(name + '[]', val);
        });
      }
    }

    // Country
    const countries = $('#select2MultipleCountry').val() || [];
    addParams('country', countries);

    // Region (Level 1)
    const level1 = $('#select2MultipleLevel1').val() || [];
    addParams('region', level1);

    // Subregion (Level 2)
    const level2 = $('#select2MultipleLevel2').val() || [];
    addParams('subregion', level2);

    // City
    const cities = $('#select2MultipleCity').val() || [];
    addParams('city', cities);

    // Price range
    const prices = $('#select2MultiplePrice').val() || [];
    addParams('price', prices);

    // Status
    const statuses = $('#select2Status').val() || [];
    addParams('status', statuses);

    // Hotel name (text input)
    const hotelName = $('#hotel_name').val();
    if (hotelName) {
      params.append('hotel_name', hotelName);
    }

    // Option 1: Reload the page with filter parameters in URL
    const baseUrl = window.location.pathname;
    const finalUrl = `${baseUrl}?${params.toString()}`;
    window.location.href = finalUrl;

    // Option 2: Update DataTable with filtered data (commented out as we're using Option 1)
    /*
    if (dt_user) {
      dt_user.ajax.url(window.routes.hotelApiFetchHotelsUrl + '?' + params.toString()).load();
    }
    */
  });

});
