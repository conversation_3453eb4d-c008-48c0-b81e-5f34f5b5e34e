@extends('layouts/layoutSavAdminMaster')

@section('title', 'Hotels Management')

<!-- Vendor Styles -->
@section('vendor-style')
@vite([
'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
'resources/assets/vendor/libs/select2/select2.scss',
'resources/assets/vendor/libs/@form-validation/form-validation.scss',
'resources/assets/vendor/libs/animate-css/animate.scss',
'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
<style>
    /* Custom styles for hotel filters accordion */
    #hotelFiltersHeader .btn-link {
        text-decoration: none;
        color: white;
        font-weight: bold;
    }

    #hotelFiltersHeader .btn-link:hover,
    #hotelFiltersHeader .btn-link:focus {
        text-decoration: none;
        color: white;
    }

    #hotelFiltersCollapse.collapse:not(.show) + .card-body {
        display: none;
    }

    #hotelFiltersCollapse.collapsing {
        height: 0;
        overflow: hidden;
        transition: height 0.35s ease;
    }

    /* Adjust select2 dropdowns to fit better in the horizontal layout */
    .select2-container {
        width: 100% !important;
    }

    /* Make the accordion header more visible */
    #hotelFiltersHeader {
        background-color: var(--bs-primary);
        border-bottom: 1px solid var(--bs-primary-border-subtle);
    }

    /* Add a pulsating animation to draw attention to the filters */
    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(13, 110, 253, 0); }
        100% { box-shadow: 0 0 0 0 rgba(13, 110, 253, 0); }
    }

    .card.mb-6 {
        animation: pulse 2s infinite;
    }
</style>
@endsection

<!-- Vendor Scripts -->
@section('vendor-script')
@vite([
'resources/assets/vendor/libs/jquery/jquery.js',
'resources/assets/vendor/libs/moment/moment.js',
'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
'resources/assets/vendor/libs/select2/select2.js',
'resources/assets/vendor/libs/@form-validation/popular.js',
'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
'resources/assets/vendor/libs/@form-validation/auto-focus.js',
'resources/assets/vendor/libs/cleavejs/cleave.js',
'resources/assets/vendor/libs/cleavejs/cleave-phone.js',
'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

<!-- Page Scripts -->
@section('page-script')

<script>
 window.routes = {
 	hotelApiFetchCountriesUrl: @json(route('api.hotel-api.countries', ['countries' => ':countries'])),
 	hotelApiFetchRegionsGroupedUrl: @json(route('api.hotel-api.regions-grouped')),

 	hotelApiFetchSubregionsUrl: @json(route('api.hotel-api.subregions')),
 	hotelApiFetchSubregionsGroupedUrl: @json(route('api.hotel-api.subregions-grouped')),

 	hotelApiFetchCitiesGroupedUrl: @json(route('api.hotel-api.cities-grouped')),
 	hotelApiFetchHotelsUrl: @json(route('api.hotel-api.hotels-list')),
 	hotelApiSearchCitiesUrl: @json(route('api.hotel-api.search-cities')),
 };

    // Initialize accordion functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Get the accordion button and collapse element
        const accordionButton = document.querySelector('#hotelFiltersHeader .btn-link');
        const collapseElement = document.querySelector('#hotelFiltersCollapse');

        // Toggle the chevron icon when the accordion is toggled
        collapseElement.addEventListener('show.bs.collapse', function() {
            accordionButton.querySelector('i:last-child').classList.remove('ti-chevron-down');
            accordionButton.querySelector('i:last-child').classList.add('ti-chevron-up');
        });

        collapseElement.addEventListener('hide.bs.collapse', function() {
            accordionButton.querySelector('i:last-child').classList.remove('ti-chevron-up');
            accordionButton.querySelector('i:last-child').classList.add('ti-chevron-down');
        });

        // Save the accordion state in localStorage
        collapseElement.addEventListener('shown.bs.collapse', function() {
            localStorage.setItem('hotelFiltersCollapsed', 'false');
        });

        collapseElement.addEventListener('hidden.bs.collapse', function() {
            localStorage.setItem('hotelFiltersCollapsed', 'true');
        });

        // Restore the accordion state from localStorage
        const isCollapsed = localStorage.getItem('hotelFiltersCollapsed') === 'true';
        if (isCollapsed) {
            collapseElement.classList.remove('show');
            accordionButton.querySelector('i:last-child').classList.remove('ti-chevron-up');
            accordionButton.querySelector('i:last-child').classList.add('ti-chevron-down');
        } else {
            accordionButton.querySelector('i:last-child').classList.remove('ti-chevron-down');
            accordionButton.querySelector('i:last-child').classList.add('ti-chevron-up');
        }

        // Show a notification to inform the user about the moved filters
        if (!localStorage.getItem('hotelFiltersMovedNotificationShown')) {
            // Use SweetAlert2 for a nice notification
            Swal.fire({
                title: 'Hotel Filters Moved',
                text: 'The Hotel Filters have been moved from the left sidebar to the top of the page as a horizontal accordion.',
                icon: 'info',
                confirmButtonText: 'Got it!',
                timer: 10000,
                timerProgressBar: true
            });

            // Mark notification as shown
            localStorage.setItem('hotelFiltersMovedNotificationShown', 'true');
        }
    });
</script>


@vite(['resources/assets/js/sav-hotel-index.js'])
@endsection

@section('content')

<div class="row g-6 mb-6">
	<div class="col-sm-6 col-xl-3">
		<div class="card">
			<div class="card-body">
				<div class="d-flex align-items-start justify-content-between">
					<div class="content-left">
						<span class="text-heading">Hoteluri</span>
						<div class="d-flex align-items-center my-1">
							<h4 class="mb-0 me-2">{{$hoteluriTotal}}</h4>
							<p class="text-success mb-0">(100%)</p>
						</div>
{{--						<small class="mb-0">Recent analytics</small>--}}
					</div>
					<div class="avatar">
						<span class="avatar-initial rounded bg-label-primary">
						  <i class="ti ti-user ti-26px"></i>
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="col-sm-6 col-xl-3">
		<div class="card">
			<div class="card-body">
				<div class="d-flex align-items-start justify-content-between">
					<div class="content-left">
						<span class="text-heading">Hoteluri partenere</span>
						<div class="d-flex align-items-center my-1">
							<h4 class="mb-0 me-2">{{$hoteluriPartenere}}</h4>
							<p class="text-success mb-0">(95%)</p>
						</div>
{{--						<small class="mb-0">Recent analytics </small>--}}
					</div>
					<div class="avatar">
						<span class="avatar-initial rounded bg-label-success">
						  <i class="ti ti-user-check ti-26px"></i>
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="col-sm-6 col-xl-3">
		<div class="card">
			<div class="card-body">
				<div class="d-flex align-items-start justify-content-between">
					<div class="content-left">
						<span class="text-heading">Hoteluri verificate</span>
						<div class="d-flex align-items-center my-1">
							<h4 class="mb-0 me-2">{{$hoteluriVerificate}}</h4>
							<p class="text-success mb-0">(0%)</p>
						</div>
{{--						<small class="mb-0">Recent analytics</small>--}}
					</div>
					<div class="avatar">
						<span class="avatar-initial rounded bg-label-danger">
						  <i class="ti ti-users ti-26px"></i>
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="col-sm-6 col-xl-3">
		<div class="card">
			<div class="card-body">
				<div class="d-flex align-items-start justify-content-between">
					<div class="content-left">
						<span class="text-heading">Hoteluri neverificate</span>
						<div class="d-flex align-items-center my-1">
							<h4 class="mb-0 me-2">{{$hoteluriVerificate}}</h4>
							<p class="text-danger mb-0">(6%)</p>
						</div>
{{--						<small class="mb-0">Recent analytics</small>--}}
					</div>
					<div class="avatar">
						<span class="avatar-initial rounded bg-label-warning">
						  <i class="ti ti-user-search ti-26px"></i>
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- ATTENTION: Hotel Filters have been moved from the left sidebar to this horizontal accordion at the top of the page -->
<!-- Hotel Filters Accordion -->
<div class="card mb-6">
    <div class="card-header bg-primary text-white" id="hotelFiltersHeader">
        <h5 class="mb-0">
            <button class="btn btn-link w-100 text-start d-flex justify-content-between align-items-center text-white" type="button" data-bs-toggle="collapse" data-bs-target="#hotelFiltersCollapse" aria-expanded="true" aria-controls="hotelFiltersCollapse">
                <span><i class="ti ti-filter me-2"></i>Hotel Filters (Moved from Left Sidebar)</span>
                <i class="ti ti-chevron-down"></i>
            </button>
        </h5>
    </div>
    <div id="hotelFiltersCollapse" class="collapse show" aria-labelledby="hotelFiltersHeader">
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="select2MultipleCountry" class="form-label">Țara</label>
                        <select id="select2MultipleCountry" class="select2-country form-select" multiple>
                            @foreach($countries as $country)
                                <option value="{{ $country->id }}" {{ in_array($country->id, $filter['country']?? []) ? 'selected' : '' }}>{{ $country->name  }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="select2MultipleLevel1" class="form-label">Level 1 (Regiune)</label>
                        <select id="select2MultipleLevel1" class="select2-level1 form-select" multiple>
                            @if(!empty($groupedRegions))
                                @foreach($groupedRegions as $country)
                                    <optgroup label="{{ $country['country'] }}">
                                        @foreach($country['regions'] as $region)
                                            <option value="{{ $region['id'] }}" {{ in_array($region['id'], $filter['region']) ? 'selected' : '' }}>{{ $region['name']  }}</option>
                                        @endforeach
                                    </optgroup>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="select2MultipleLevel2" class="form-label">Level 2 (Sub-regiune)</label>
                        <select id="select2MultipleLevel2" class="select2-level2 form-select" multiple>
                            @if(!empty($groupedSubregions))
                                @foreach($groupedSubregions as $region)
                                    <optgroup label="{{ $region['region'] }}">
                                        @foreach($region['subregions'] as $subregion)
                                            <option value="{{ $subregion['id'] }}" {{ in_array($subregion['id'], $filter['subregion']) ? 'selected' : '' }}>{{ $subregion['name']  }}</option>
                                        @endforeach
                                    </optgroup>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="select2MultipleCity" class="form-label">Oraș</label>
                        <select id="select2MultipleCity" class="select2-city form-select" multiple>
                            <!-- Cities will be loaded via AJAX when user types at least 3 characters -->
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="hotel_name" class="form-label">Nume hotel</label>
                        <input type="text" id="hotel_name" class="form-control" placeholder="Nume hotel" value="{{ $filter['hotel_name'] ?? '' }}" />
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="select2MultiplePrice" class="form-label">Estimare preț</label>
                        <select id="select2MultiplePrice" class="select2-price form-select" multiple>
                            @foreach($priceRanges as $priceRange)
                                <option value="{{ $priceRange['value'] }}" {{ in_array($priceRange['value'], $filter['price'] ?? []) ? 'selected' : '' }}>
                                    {{ $priceRange['label'] }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="select2Status" class="form-label">Status</label>
                        <select id="select2Status" class="select2-status form-select" multiple>
                            @foreach($statuses as $status)
                                <option value="{{ $status['value'] }}" {{ in_array($status['value'], $filter['status'] ?? []) ? 'selected' : '' }}>
                                    {{ $status['label'] }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-10">
                    <div class="mb-3">
                        <div class="col-12">
                            <br>
                            <div class="d-flex justify-content-end gap-2">
                                <button type="button" class="btn btn-primary d-flex align-items-center btn-filter">
                                    <i class="fa-solid fa-filter me-2"></i>
                                    Filter
                                </button>
                                <button type="button" class="btn btn-primary d-flex align-items-center">
                                    <i class="fa-solid fa-map-location-dot me-2"></i>
                                    View on map
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hotels List Table -->
<div class="card">
	<div class="card-header border-bottom">
		<h5 class="card-title mb-0">Hotels: Search Filter</h5>
	</div>
	<div class="card-datatable table-responsive">
		<table class="datatables-teams table">
			<thead class="border-top">
			<tr>
				<th></th>
				<th>Id</th>
				<th>Hotel</th>
				<th>Oraș</th>
				<th>Țara</th>
				<th>Level 1 Div.</th>
				<th>Level 2 Div.</th>
				<th>Pret</th>
				<th>Status</th>
				<th>Actions</th>
			</tr>
			</thead>
		</table>
	</div>

	<!-- Offcanvas to add new user -->
	<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAddTeam" aria-labelledby="offcanvasAddTeamLabel">
		<div class="offcanvas-header border-bottom">
			<h5 id="offcanvasAddTeamLabel" class="offcanvas-title">Add Team</h5>
			<button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
		</div>
		<div class="offcanvas-body mx-0 flex-grow-0 p-6 h-100">
			<form class="add-new-team pt-0" id="addNewTeamForm" action="{{ route('teams.api.store') }}" method="POST">
				<input type="hidden" name="id" id="team_id">
				<div class="mb-6">
					<label class="form-label" for="add-team-name">Agentie</label>
					<input type="text" class="form-control" id="add-team-name" placeholder="Oradea" name="name" aria-label="Oradea" />
				</div>

				<button type="submit" class="btn btn-primary me-3 data-submit">Submit</button>
				<button type="reset" class="btn btn-label-danger" data-bs-dismiss="offcanvas">Cancel</button>
			</form>
		</div>
	</div>
</div>
@endsection
