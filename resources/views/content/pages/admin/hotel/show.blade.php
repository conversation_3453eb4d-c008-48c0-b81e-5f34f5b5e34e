@extends('layouts/layoutMaster')

@section('title', 'Hotel - View')

<!-- Vendor Styles -->
@section('vendor-style')
    @vite([
      'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
      'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
      'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
      'resources/assets/vendor/libs/animate-css/animate.scss',
      'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
    ])
@endsection

<!-- Vendor Scripts -->
@section('vendor-script')
    @vite([
      'resources/assets/vendor/libs/jquery/jquery.js',
      'resources/assets/vendor/libs/moment/moment.js',
      'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
      'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
    ])
@endsection

@section('content')
    <!-- Hotel View -->
    <div class="card">
        <div class="card-header border-bottom">
            <h5 class="card-title mb-0">Hotel Details: {{ $hotel->name }}</h5>
        </div>
        <div class="card-body">
            @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif

            <!-- Basic Information -->
            <div class="col-12">
                <h6 class="fw-semibold">Basic Information</h6>
                <hr class="mt-0" />
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>Hotel Name:</strong> {{ $hotel->name }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Stars:</strong> {{ $hotel->stars ?? '-' }}</p>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <p><strong>Description 1:</strong></p>
                    <p>{{ $hotel->description_1 ?? '-' }}</p>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <p><strong>Description 2:</strong></p>
                    <p>{{ $hotel->description_2 ?? '-' }}</p>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <p><strong>Description 3:</strong></p>
                    <p>{{ $hotel->description_3 ?? '-' }}</p>
                </div>
            </div>

            <!-- Location Information -->
            <div class="col-12 mt-4">
                <h6 class="fw-semibold">Location Information</h6>
                <hr class="mt-0" />
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>Country:</strong> {{ $hotel->country->name ?? '-' }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Region:</strong> {{ $hotel->level1Division->name ?? '-' }}</p>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>Subregion:</strong> {{ $hotel->level2Division->name ?? '-' }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>City:</strong> {{ $hotel->city->name ?? '-' }}</p>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <p><strong>Full Address:</strong> {{ $hotel->full_address ?? '-' }}</p>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <p><strong>Street 1:</strong> {{ $hotel->street_1 ?? '-' }}</p>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <p><strong>Street 2:</strong> {{ $hotel->street_2 ?? '-' }}</p>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>Postal Code:</strong> {{ $hotel->postal_code ?? '-' }}</p>
                </div>
                <div class="col-md-3">
                    <p><strong>Latitude:</strong> {{ $hotel->latitude ?? '-' }}</p>
                </div>
                <div class="col-md-3">
                    <p><strong>Longitude:</strong> {{ $hotel->longitude ?? '-' }}</p>
                </div>
            </div>

            <!-- Hotel Details -->
            <div class="col-12 mt-4">
                <h6 class="fw-semibold">Hotel Details</h6>
                <hr class="mt-0" />
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>Price Range:</strong> {{ $hotel->price_range ? $hotel->price_range->getLabel() : '-' }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Most recent avg. price per person:</strong> {{ $hotel->recent_avg_price_per_person ?? '-' }}</p>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>Partner Type:</strong> {{ $hotel->partner_type ? $hotel->partner_type->name : '-' }}</p>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <p><strong>Hotel Types:</strong> 
                        @if($hotel->types->count() > 0)
                            {{ $hotel->types->pluck('name')->join(', ') }}
                        @else
                            -
                        @endif
                    </p>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-3">
                    <p><strong>Capacity (Rooms):</strong> <i class="fa-solid fa-bed"></i> {{ $hotel->capacity_rooms ?? '-' }}</p>
                </div>
                <div class="col-md-3">
                    <p><strong>Capacity (Guests):</strong> <i class="fa-solid fa-user-friends"></i> {{ $hotel->capacity_guests ?? '-' }}</p>
                </div>
                <div class="col-md-3">
                    <p><strong>Breakfast:</strong> 
                        @if($hotel->breakfast === null)
                            I don't know
                        @elseif($hotel->breakfast)
                            Included
                        @else
                            Not included
                        @endif
                    </p>
                </div>
                <div class="col-md-3">
                    <p><strong>Accept Groups:</strong> 
                        @if($hotel->accept_groups === null)
                            I don't know
                        @elseif($hotel->accept_groups)
                            Accept
                        @else
                            Doesn't accept
                        @endif
                    </p>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="col-12 mt-4">
                <h6 class="fw-semibold">Contact Information</h6>
                <hr class="mt-0" />
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>Website:</strong> 
                        @if($hotel->website)
                            <a href="{{ $hotel->website }}" target="_blank">{{ $hotel->website }}</a>
                        @else
                            -
                        @endif
                    </p>
                </div>
                <div class="col-md-6">
                    <p><strong>Phone:</strong> {{ $hotel->phone_international ?? '-' }}</p>
                </div>
            </div>

            <!-- Social Media Links -->
            <div class="col-12 mt-4">
                <h6 class="fw-semibold">Social Media Links</h6>
                <hr class="mt-0" />
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>Website Link:</strong> 
                        @if($hotel->link)
                            <a href="{{ $hotel->link }}" target="_blank">{{ $hotel->link }}</a>
                        @else
                            -
                        @endif
                    </p>
                </div>
                <div class="col-md-6">
                    <p><strong>Facebook:</strong> 
                        @if($hotel->facebook_link)
                            <a href="{{ $hotel->facebook_link }}" target="_blank">{{ $hotel->facebook_link }}</a>
                        @else
                            -
                        @endif
                    </p>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>YouTube:</strong> 
                        @if($hotel->youtube_link)
                            <a href="{{ $hotel->youtube_link }}" target="_blank">{{ $hotel->youtube_link }}</a>
                        @else
                            -
                        @endif
                    </p>
                </div>
                <div class="col-md-6">
                    <p><strong>Twitter:</strong> 
                        @if($hotel->twitter_link)
                            <a href="{{ $hotel->twitter_link }}" target="_blank">{{ $hotel->twitter_link }}</a>
                        @else
                            -
                        @endif
                    </p>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>Instagram:</strong> 
                        @if($hotel->instagram_link)
                            <a href="{{ $hotel->instagram_link }}" target="_blank">{{ $hotel->instagram_link }}</a>
                        @else
                            -
                        @endif
                    </p>
                </div>
                <div class="col-md-6">
                    <p><strong>LinkedIn:</strong> 
                        @if($hotel->linkedin_link)
                            <a href="{{ $hotel->linkedin_link }}" target="_blank">{{ $hotel->linkedin_link }}</a>
                        @else
                            -
                        @endif
                    </p>
                </div>
            </div>

            <!-- Hotel Contacts -->
            <div class="col-12 mt-4">
                <h6 class="fw-semibold">Hotel Contacts</h6>
                <hr class="mt-0" />
            </div>

            <div class="col-12">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Notes</th>
                                <th>Default</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if(isset($contacts) && $contacts->count() > 0)
                                @foreach($contacts as $contact)
                                <tr>
                                    <td>{{ $contact->name ?? '-' }}</td>
                                    <td>{{ $contact->email }}</td>
                                    <td>{{ $contact->phone ?? '-' }}</td>
                                    <td>{{ $contact->notes ?? '-' }}</td>
                                    <td class="text-center">
                                        @if($contact->default)
                                            <i class="ti ti-check text-success"></i>
                                        @else
                                            <i class="ti ti-x text-danger"></i>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td colspan="5" class="text-center">-</td>
                                </tr>
                            @endif
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Notes -->
            <div class="col-12 mt-4">
                <h6 class="fw-semibold">Notes</h6>
                <hr class="mt-0" />
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>Price Notes:</strong></p>
                    <p>{{ $hotel->notes_prices ?? '-' }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>General Notes:</strong></p>
                    <p>{{ $hotel->notes ?? '-' }}</p>
                </div>
            </div>

            <!-- Hotel Photos -->
            <div class="col-12 mt-4">
                <h6 class="fw-semibold">Hotel Photos</h6>
                <hr class="mt-0" />
            </div>

            @if(isset($photos) && $photos->count() > 0)
            <div class="col-12">
                <div class="d-flex flex-wrap gap-3 mb-3">
                    @foreach($photos as $photo)
                    <div class="position-relative">
                        <img src="{{ asset('storage/' . $photo->path_thumb) }}" alt="Hotel Photo" class="rounded" style="width: 150px; height: 100px; object-fit: cover;">
                        @if($photo->default)
                            <span class="badge bg-primary position-absolute top-0 end-0 m-1">Default</span>
                        @endif
                    </div>
                    @endforeach
                </div>
            </div>
            @else
            <div class="col-12">
                <p class="text-muted">-</p>
            </div>
            @endif

            <!-- Action Buttons -->
            <div class="col-12 mt-4">
                <a href="{{ route('hotel.edit', $hotel) }}" class="btn btn-primary me-2">Edit Hotel</a>
                <a href="{{ route('hotel.index') }}" class="btn btn-label-secondary">Back to List</a>
            </div>
        </div>
    </div>
@endsection
