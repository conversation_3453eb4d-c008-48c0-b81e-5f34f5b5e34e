@extends('layouts/layoutMaster')

@section('title', 'User Management - Crud App')

<!-- Vendor Styles -->
@section('vendor-style')
    @vite([
      'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
      'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
      'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
      'resources/assets/vendor/libs/select2/select2.scss',
      'resources/assets/vendor/libs/@form-validation/form-validation.scss',
      'resources/assets/vendor/libs/animate-css/animate.scss',
      'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
    ])
@endsection

<!-- Vendor Scripts -->
@section('vendor-script')
    @vite([
      'resources/assets/vendor/libs/moment/moment.js',
      'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
      'resources/assets/vendor/libs/select2/select2.js',
      'resources/assets/vendor/libs/@form-validation/popular.js',
      'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
      'resources/assets/vendor/libs/@form-validation/auto-focus.js',
      'resources/assets/vendor/libs/cleavejs/cleave.js',
      'resources/assets/vendor/libs/cleavejs/cleave-phone.js',
      'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
    ])
@endsection

<!-- Page Scripts -->
@section('page-script')
    <script>
      window.routes = {
        teamApiEditUrl: @json(route('teams.api.edit', ['team' => ':id'])),
        teamApiUpdateUrl: @json(route('teams.api.update', ['team' => ':id'])),
        teamApiStoreUrl: @json(route('teams.api.store', [])),
      };
    </script>

    @vite(['resources/assets/js/app-access-team.js'])
@endsection

@section('content')
    @if (false)
    <div class="row g-6 mb-6">
        <div class="col-sm-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span class="text-heading">Users</span>
                            <div class="d-flex align-items-center my-1">
{{--                                <h4 class="mb-0 me-2">{{$totalUser}}</h4>--}}
                                <p class="text-success mb-0">(100%)</p>
                            </div>
                            <small class="mb-0">Total Users</small>
                        </div>
                        <div class="avatar">
            <span class="avatar-initial rounded bg-label-primary">
              <i class="ti ti-user ti-26px"></i>
            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span class="text-heading">Verified Users</span>
                            <div class="d-flex align-items-center my-1">
{{--                                <h4 class="mb-0 me-2">{{$verified}}</h4>--}}
                                <p class="text-success mb-0">(+95%)</p>
                            </div>
                            <small class="mb-0">Recent analytics </small>
                        </div>
                        <div class="avatar">
            <span class="avatar-initial rounded bg-label-success">
              <i class="ti ti-user-check ti-26px"></i>
            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span class="text-heading">Duplicate Users</span>
                            <div class="d-flex align-items-center my-1">
{{--                                <h4 class="mb-0 me-2">{{$userDuplicates}}</h4>--}}
                                <p class="text-success mb-0">(0%)</p>
                            </div>
                            <small class="mb-0">Recent analytics</small>
                        </div>
                        <div class="avatar">
            <span class="avatar-initial rounded bg-label-danger">
              <i class="ti ti-users ti-26px"></i>
            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span class="text-heading">Verification Pending</span>
                            <div class="d-flex align-items-center my-1">
{{--                                <h4 class="mb-0 me-2">{{$notVerified}}</h4>--}}
                                <p class="text-danger mb-0">(+6%)</p>
                            </div>
                            <small class="mb-0">Recent analytics</small>
                        </div>
                        <div class="avatar">
            <span class="avatar-initial rounded bg-label-warning">
              <i class="ti ti-user-search ti-26px"></i>
            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
    <!-- Users List Table -->
    <div class="card">
        <div class="card-header border-bottom">
            <h5 class="card-title mb-0">Team: Search Filter</h5>
        </div>
        <div class="card-datatable table-responsive">
            <table class="datatables-teams table">
                <thead class="border-top">
                <tr>
                    <th></th>
                    <th>Id</th>
                    <th>Team</th>
                    <th>Users</th>
                    <th>Actions</th>
                </tr>
                </thead>
            </table>
        </div>
        <!-- Offcanvas to add new user -->
        <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAddTeam" aria-labelledby="offcanvasAddTeamLabel">
            <div class="offcanvas-header border-bottom">
                <h5 id="offcanvasAddTeamLabel" class="offcanvas-title">Add Team</h5>
                <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body mx-0 flex-grow-0 p-6 h-100">
                <form class="add-new-team pt-0" id="addNewTeamForm" action="{{ route('teams.api.store') }}" method="POST">
                    <input type="hidden" name="id" id="team_id">
                    <div class="mb-6">
                        <label class="form-label" for="add-team-name">Agentie</label>
                        <input type="text" class="form-control" id="add-team-name" placeholder="Oradea" name="name" aria-label="Oradea" />
                    </div>

                    <button type="submit" class="btn btn-primary me-3 data-submit">Submit</button>
                    <button type="reset" class="btn btn-label-danger" data-bs-dismiss="offcanvas">Cancel</button>
                </form>
            </div>
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-end gap-2">
                <button type="button" class="btn btn-primary d-flex align-items-center">
                    <i class="fa-solid fa-filter me-2"></i>
                    Filter
                </button>
                <button type="button" class="btn btn-primary d-flex align-items-center">
                    <i class="fa-solid fa-map-location-dot me-2"></i>
                    View on map
                </button>
            </div>
        </div>
    </div>
@endsection
