@php
    $configData = Helper::appClasses();
    $user = auth()->user();
    $currentTeamId = $user->current_team_id;
    
    // Force set the teams context
    if ($currentTeamId) {
        setPermissionsTeamId($currentTeamId);
    }
    
    // Debug information
    $debugInfo = [
        'user_id' => $user->id,
        'current_team_id' => $currentTeamId,
            'current_team_name' => $user->currentTeam?->name,
        'permissions_team_id' => getPermissionsTeamId(),
        'roles_from_db' => Illuminate\Support\Facades\DB::table('model_has_roles')
            ->where('model_id', $user->id)
            ->where('model_type', get_class($user))
            ->get(),
        'roles_from_getRoleNames' => $user->getRoleNames(),
            'roles_from_roles_relation' => $user->roles()->get(),
    ];
    
    // Get all roles from database
    $allRoles = Illuminate\Support\Facades\DB::table('roles')->get();
    
    // Get user's roles from model_has_roles
    $userRoles = Illuminate\Support\Facades\DB::table('model_has_roles')
        ->where('model_id', $user->id)
        ->where('model_type', get_class($user))
        ->get();
        
    // Get role details for user's roles
    $userRoleDetails = Illuminate\Support\Facades\DB::table('roles')
        ->whereIn('id', $userRoles->pluck('role_id'))
        ->get();

	$myCurrentRoles = $user->getRoleNames();
	$myCurrentTeam = $user->currentTeam;
@endphp

@extends('layouts.layoutSavAdminMaster')

@section('title', 'Home')

@section('content')
    <h4>Dashboard Page</h4>
    @auth
        <div>Autentificat</div>
        <div>
            <ul>
                <li>
                    {{ $user->name }}
                </li>
                <li>
                    {{ $user->email }}
                </li>
                <li>
                    User ID: {{ $user->id }}
                </li>
                <li>
                    Current Team ID: {{ $currentTeamId }}
                </li>
                <li>
                    Current Team Name: {{ $user->currentTeam?->name }}
                </li>
                <li>
                    <h5>Debug Information:</h5>
{{--                    <pre>{{ print_r($debugInfo, true) }}</pre>--}}
                </li>
                <li>
                    <h5>All Roles in System:</h5>
                    <ul>
                        @foreach($allRoles as $role)
                            <li>ID: {{ $role->id }}, Name: {{ $role->name }}</li>
                        @endforeach
                    </ul>
                </li>
                <li>
                    <h5>User's Roles from model_has_roles:</h5>
                    <ul>
                        @foreach($userRoles as $userRole)
                            <li>Role ID: {{ $userRole->role_id }}, Team ID: {{ $userRole->team_id }}</li>
                        @endforeach
                    </ul>
                </li>
                <li>
                    <h5>User's Role Details:</h5>
                    <ul>
                        @foreach($userRoleDetails as $role)
                            <li>ID: {{ $role->id }}, Name: {{ $role->name }}</li>
                        @endforeach
                    </ul>
                </li>
                <li>
                    <h5>Adrian Roles:</h5>
                    <ul>
{{--                        @foreach($myCurrentRoles as $role)--}}
{{--                            <li>ID: {{ $role->id }}, Name: {{ $role->name }}</li>--}}
{{--                        @endforeach--}}
                    </ul>
                </li>
                <li>
                    <h5>Adrian Current Team:</h5>
                    <ul>
{{--                        <li>{{ $myCurrentTeam->name }}</li>--}}
                    </ul>
                </li><li>
                    <h5>Adrian Current Role:</h5>
                    <ul>
{{--                        <li>{{ print_r($user->roles()->first()->toArray(), true) }}</li>--}}
                    </ul>
                </li>
                <li>
                    <h5>Current team id</h5>
{{--                    Current Team ID: {{ $user->currentTeam->id }}--}}
                </li>



            </ul>
        </div>
    @endauth
@endsection
