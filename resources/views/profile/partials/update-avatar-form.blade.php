<div>
    <div class="d-flex align-items-start align-items-sm-center gap-4">
        @if(auth()->user()->avatar)
            <img src="{{ asset('storage/' . auth()->user()->avatar) }}" alt="{{ auth()->user()->name }}" class="d-block rounded" height="100" width="100" id="uploadedAvatar" />
        @else
            <img src="{{ asset('assets/img/avatars/1.png') }}" alt="{{ auth()->user()->name }}" class="d-block rounded" height="100" width="100" id="uploadedAvatar" />
        @endif
        <div class="button-wrapper">
            <form action="{{ route('profile.avatar') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <label for="upload" class="btn btn-primary me-2 mb-4" tabindex="0">
                    <span class="d-none d-sm-block">Upload new photo</span>
                    <i class="ti ti-upload d-block d-sm-none"></i>
                    <input type="file" id="upload" name="avatar" class="account-file-input" hidden accept="image/png, image/jpeg, image/gif" onchange="document.getElementById('submit-avatar').click();" />
                </label>
                <button type="submit" id="submit-avatar" class="d-none">Submit</button>
                <button type="button" class="btn btn-label-secondary account-image-reset mb-4">
                    <i class="ti ti-refresh-dot d-block d-sm-none"></i>
                    <span class="d-none d-sm-block">Reset</span>
                </button>

                <p class="mb-0">Allowed JPG, GIF or PNG. Max size of 2MB</p>
                
                @error('avatar')
                    <div class="text-danger mt-2">{{ $message }}</div>
                @enderror
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const resetButton = document.querySelector('.account-image-reset');
    const fileInput = document.querySelector('.account-file-input');
    const previewImage = document.getElementById('uploadedAvatar');
    const defaultImage = "{{ asset('assets/img/avatars/1.png') }}";
    
    resetButton.addEventListener('click', function() {
        fileInput.value = '';
        if (previewImage) {
            previewImage.src = defaultImage;
        }
    });
});
</script>