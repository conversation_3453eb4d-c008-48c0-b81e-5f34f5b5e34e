<div>
    <h6>Connected Accounts</h6>
    <p class="mb-4">Connect your accounts to enable social login and easier access to our services.</p>
    
    <div class="d-flex">
        <div class="flex-shrink-0">
            <i class="fab fa-microsoft me-3"></i>
        </div>
        <div class="flex-grow-1 row">
            <div class="col-9 mb-sm-0 mb-2">
                <h6 class="mb-0">Microsoft</h6>
                <small class="text-muted">Sign in using Microsoft account</small>
            </div>
            <div class="col-3 text-end">
                @if(auth()->user()->provider === 'microsoft')
                    <div class="form-check form-switch">
                        <input class="form-check-input float-end" type="checkbox" role="switch" checked disabled>
                    </div>
                    <form action="{{ route('profile.social.disconnect') }}" method="POST" class="mt-1">
                        @csrf
                        <button type="submit" class="btn btn-sm btn-label-danger">Disconnect</button>
                    </form>
                @else
                    <a href="{{ route('profile.social.connect', 'microsoft') }}" class="btn btn-sm btn-label-primary">Connect</a>
                @endif
            </div>
        </div>
    </div>
    
    <div class="d-flex">
        <div class="flex-shrink-0">
            <i class="fab fa-google me-3"></i>
        </div>
        <div class="flex-grow-1 row">
            <div class="col-9 mb-sm-0 mb-2">
                <h6 class="mb-0">Google</h6>
                <small class="text-muted">Sign in using Google account</small>
            </div>
            <div class="col-3 text-end">
                @if(auth()->user()->provider === 'google')
                    <div class="form-check form-switch">
                        <input class="form-check-input float-end" type="checkbox" role="switch" checked disabled>
                    </div>
                    <form action="{{ route('profile.social.disconnect') }}" method="POST" class="mt-1">
                        @csrf
                        <button type="submit" class="btn btn-sm btn-label-danger">Disconnect</button>
                    </form>
                @else
                    <a href="{{ route('profile.social.connect', 'google') }}" class="btn btn-sm btn-label-primary">Connect</a>
                @endif
            </div>
        </div>
    </div>
</div>