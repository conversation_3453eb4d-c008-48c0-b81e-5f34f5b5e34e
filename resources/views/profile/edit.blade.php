{{--@extends('layouts.app')--}}
@extends('layouts/layoutSavAdminMaster')

@section('title', 'Profile')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
    <h4 class="fw-bold py-3 mb-4">
        <span class="text-muted fw-light">Account Settings /</span> Profile
    </h4>

    <div class="row">
        <div class="col-md-12">
            @if(session('status') === 'profile-updated')
                <div class="alert alert-success alert-dismissible" role="alert">
                    Profile information updated successfully.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if(session('status') === 'avatar-updated')
                <div class="alert alert-success alert-dismissible" role="alert">
                    Profile avatar updated successfully.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if(session('status') === 'social-connected')
                <div class="alert alert-success alert-dismissible" role="alert">
                    Social account connected successfully.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if(session('status') === 'social-disconnected')
                <div class="alert alert-success alert-dismissible" role="alert">
                    Social account disconnected successfully.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <!-- Profile Details -->
            <div class="card mb-4">
                <h5 class="card-header">Profile Details</h5>
                <div class="card-body">
                    @include('profile.partials.update-profile-information-form')
                </div>
            </div>

            <!-- Avatar -->
            <div class="card mb-4">
                <h5 class="card-header">Profile Avatar</h5>
                <div class="card-body">
                    @include('profile.partials.update-avatar-form')
                </div>
            </div>

            <!-- Social Accounts -->
            <div class="card mb-4">
                <h5 class="card-header">Social Accounts</h5>
                <div class="card-body">
                    @include('profile.partials.social-accounts-form')
                </div>
            </div>

            <!-- Change Password -->
            <div class="card mb-4">
                <h5 class="card-header">Change Password</h5>
                <div class="card-body">
                    @include('profile.partials.update-password-form')
                </div>
            </div>

            <!-- Delete Account -->
            <div class="card mb-4">
                <h5 class="card-header">Delete Account</h5>
                <div class="card-body">
                    @include('profile.partials.delete-user-form')
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
