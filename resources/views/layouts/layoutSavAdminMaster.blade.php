@isset($pageConfigs)
{!! Helper::updatePageConfig($pageConfigs) !!}
@endisset
@php
$configData = Helper::appClasses();
@endphp

@isset($configData["layout"])
    @include(
	(( $configData["layout"] === 'horizontal') ? 'layouts.horizontalSavAdminLayout' :
    (( $configData["layout"] === 'blank') ? 'layouts.blankSavAdminLayout' :
    (($configData["layout"] === 'front') ? 'layouts.layoutSavFront' : 'layouts.contentSavAdminNavbarLayout') )))
@endisset
