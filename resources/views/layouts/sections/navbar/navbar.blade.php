@php
  use Illuminate\Support\Facades\Auth;
  use Illuminate\Support\Facades\Route;
  $containerNav = ($configData['contentLayout'] === 'compact') ? 'container-xxl' : 'container-fluid';
  $navbarDetached = ($navbarDetached ?? '');
@endphp

        <!-- Navbar -->
@if(isset($navbarDetached) && $navbarDetached == 'navbar-detached')
  <nav class="layout-navbar {{$containerNav}} navbar navbar-expand-xl {{$navbarDetached}} align-items-center bg-navbar-theme"
       id="layout-navbar">
    @endif
    @if(isset($navbarDetached) && $navbarDetached == '')
      <nav class="layout-navbar navbar navbar-expand-xl align-items-center bg-navbar-theme" id="layout-navbar">
        <div class="{{$containerNav}}">
          @endif

          <!--  Brand demo (display only for navbar-full and hide on below xl) -->
          @if(isset($navbarFull))
            <div class="navbar-brand app-brand demo d-none d-xl-flex py-0 me-4">
              <a href="{{url('/')}}" class="app-brand-link">
                <span class="app-brand-logo demo">@include('_partials.macros',["height"=>20])</span>
                <span class="app-brand-text demo menu-text fw-bold">{{config('variables.templateName')}}</span>
              </a>
            </div>
          @endif

          <!-- ! Not required for layout-without-menu -->
          @if(!isset($navbarHideToggle))
            <div class="layout-menu-toggle navbar-nav align-items-xl-center me-3 me-xl-0{{ isset($menuHorizontal) ? ' d-xl-none ' : '' }} {{ isset($contentNavbar) ?' d-xl-none ' : '' }}">
              <a class="nav-item nav-link px-0 me-xl-4" href="javascript:void(0)">
                <i class="ti ti-menu-2 ti-md"></i>
              </a>
            </div>
          @endif

          <!-- Impersonation Alert -->
{{--          @if(session()->has('impersonator_id'))--}}
{{--            <div class="alert alert-warning d-flex justify-content-between align-items-center mb-0 w-100 py-2">--}}
{{--              <div>--}}
{{--                <i class="ti ti-user-exclamation me-2"></i>--}}
{{--                You are impersonating another user.--}}
{{--              </div>--}}
{{--              <a href="{{ route('users.stop-impersonating') }}" class="btn btn-sm btn-warning">--}}
{{--                <i class="ti ti-user-off me-1"></i> Stop Impersonating--}}
{{--              </a>--}}
{{--            </div>--}}
{{--          @endif--}}
          <!-- / Impersonation Alert -->

          <div class="navbar-nav-right d-flex align-items-center" id="navbar-collapse">

            @if($configData['hasCustomizer'] == true)
              <!-- Style Switcher -->
              <div class="navbar-nav align-items-center">
                <div class="nav-item dropdown-style-switcher dropdown me-2 me-xl-0">
                  <a class="nav-link btn btn-text-secondary btn-icon rounded-pill dropdown-toggle hide-arrow"
                     href="javascript:void(0);" data-bs-toggle="dropdown">
                    <i class='ti ti-md'></i>
                  </a>
                  <ul class="dropdown-menu dropdown-menu-start dropdown-styles">
                    <li>
                      <a class="dropdown-item" href="javascript:void(0);" data-theme="light">
                        <span class="align-middle"><i class='ti ti-sun ti-md me-3'></i>Light</span>
                      </a>
                    </li>
                    <li>
                      <a class="dropdown-item" href="javascript:void(0);" data-theme="dark">
                        <span class="align-middle"><i class="ti ti-moon-stars ti-md me-3"></i>Dark</span>
                      </a>
                    </li>
                    <li>
                      <a class="dropdown-item" href="javascript:void(0);" data-theme="system">
                        <span class="align-middle"><i
                                  class="ti ti-device-desktop-analytics ti-md me-3"></i>System</span>
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
              <!--/ Style Switcher -->
            @endif

            <ul class="navbar-nav flex-row align-items-center ms-auto">

              <!-- User -->
              <li class="nav-item navbar-dropdown dropdown-user dropdown">
                <a class="nav-link dropdown-toggle hide-arrow p-0" href="javascript:void(0);" data-bs-toggle="dropdown">
                  <div class="avatar avatar-online">
                    <img src="{{ (Auth::user() && Auth::user()->profile_photo_url) ? Auth::user()->profile_photo_url : asset('assets/img/avatars/1.png') }}"
                         alt class="rounded-circle">
                  </div>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                  <li>
                    <a class="dropdown-item mt-0"
                       href="{{ Route::has('profile.show') ? route('profile.show') : 'javascript:void(0);' }}">
                      <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-2">
                          <div class="avatar avatar-online">
                            <img src="{{ (Auth::user() && Auth::user()->profile_photo_url) ? Auth::user()->profile_photo_url : asset('assets/img/avatars/1.png') }}"
                                 alt class="rounded-circle">
                          </div>
                        </div>
                        <div class="flex-grow-1">
                          <h6 class="mb-0">
                            @if (Auth::check())
                              {{ Auth::user()->name }}
                            @else
                              John Doe
                            @endif
                          </h6>
                          @auth
                            <small class="text-muted">{{ Auth::user()->currentTeam?->name }}</small>
                          @endauth
                        </div>
                      </div>
                    </a>
                  </li>
                  <li>
                    <div class="dropdown-divider my-1 mx-n2"></div>
                  </li>
                  <li>
                    <a class="dropdown-item"
                       href="{{ Route::has('profile.edit') ? route('profile.edit') : 'javascript:void(0);' }}">
                      <i class="ti ti-user me-3 ti-md"></i><span class="align-middle">My Profile</span>
                    </a>
                  </li>
                  @auth
                    @if (false && Laravel\Jetstream\Jetstream::hasApiFeatures())
                      <li>
                        <a class="dropdown-item" href="{{ route('api-tokens.index') }}">
                          <i class="ti ti-key ti-md me-3"></i><span class="align-middle">API Tokens</span>
                        </a>
                      </li>
                    @endif
                  @endif
                  @if (false)
                    <li>
                      <a class="dropdown-item" href="javascript:void(0);">
                  <span class="d-flex align-items-center align-middle">
                    <i class="flex-shrink-0 ti ti-file-dollar me-3 ti-md"></i><span class="flex-grow-1 align-middle">Billing</span>
                    <span class="flex-shrink-0 badge bg-danger d-flex align-items-center justify-content-center">4</span>
                  </span>
                      </a>
                    </li>
                  @endif

                  {{--              @if (Auth::User() && Laravel\Jetstream\Jetstream::hasTeamFeatures())--}}
                  @if (Auth::User() && Auth::user()->can(\App\Enums\PermissionEnum::SWITCH_TEAM))
                    <li>
                      <div class="dropdown-divider my-1 mx-n2"></div>
                    </li>
                    <li>
                      <h6 class="dropdown-header">Manage Team</h6>
                    </li>
                    <li>
                      <div class="dropdown-divider my-1 mx-n2"></div>
                    </li>
                    <li>
                      {{--                  <a class="dropdown-item" href="{{ Auth::user() ? route('teams.show', Auth::user()->currentTeam->id) : 'javascript:void(0)' }}">--}}
                      <i class="ti ti-settings ti-md me-3"></i><span class="align-middle">Team Settings</span>
                      {{--                  </a>--}}
                    </li>
                    {{--                @can('create', Laravel\Jetstream\Jetstream::newTeamModel())--}}
                    {{--                  <li>--}}
                    {{--                    <a class="dropdown-item" href="{{ route('teams.create') }}">--}}
                    {{--                      <i class="ti ti-user ti-md me-3"></i><span class="align-middle">Create New Team</span>--}}
                    {{--                    </a>--}}
                    {{--                  </li>--}}
                    {{--                @endcan--}}

                    @if (Auth::user()->teams->count() > 1)
                      <li>
                        <div class="dropdown-divider my-1 mx-n2"></div>
                      </li>
                      <li>
                        <h6 class="dropdown-header">Switch Teams</h6>
                      </li>
                      <li>
                        <div class="dropdown-divider my-1 mx-n2"></div>
                      </li>
                    @endif

                    @if (Auth::user())
                      <ul class="dropdown-menux dropdown-menu-endx">
                        @foreach (Auth::user()->teams as $team)
                          <li>
                            <form method="GET" action="{{ route('teams.change', $team->id) }}">
                              @csrf
                              <button type="submit" class="dropdown-item">
                                {{ $team->name }}
                                @if (Auth::user()->current_team_id === $team->id)
                                  <span class="badge bg-primary ms-2">Current</span>
                                @endif
                              </button>
                            </form>
                          </li>

                          {{-- Below commented code read by artisan command while installing jetstream. !! Do not remove if you want to use jetstream. --}}

                          {{--                      <li>{{$team->name}}</li>--}}
                          {{-- <x-switchable-team :team="$team" /> --}}
                        @endforeach
                      </ul>
                    @endif

                  @endif
                  <li>
                    <div class="dropdown-divider my-1 mx-n2"></div>
                  </li>

                  @if(Auth::user())
                    <li>
                      <div class="d-grid px-2 pt-2 pb-1">
                        <a class="btn btn-sm btn-danger d-flex" href="{{ route('logout') }}"
                           onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                          <small class="align-middle">Logout</small>
                          <i class="ti ti-logout ms-2 ti-14px"></i>
                        </a>
                      </div>
                    </li>
                    <form method="POST" id="logout-form" action="{{ route('logout') }}">
                      @csrf
                    </form>
                  @else
                    <li>
                      <div class="d-grid px-2 pt-2 pb-1">
                        <a class="btn btn-sm btn-danger d-flex"
                           href="{{ Route::has('login') ? route('login') : url('auth/login-basic') }}">
                          <small class="align-middle">Login</small>
                          <i class="ti ti-login ms-2 ti-14px"></i>
                        </a>
                      </div>
                    </li>
                  @endif
                </ul>
              </li>
              <!--/ User -->
            </ul>
          </div>

          @if(!isset($navbarDetached))
        </div>
        @endif
      </nav>
      <!-- / Navbar -->
