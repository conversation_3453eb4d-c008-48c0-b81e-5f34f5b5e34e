@extends('layouts/layoutSavAdminMaster')

@section('title', 'View Email')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
    <h4 class="fw-bold py-3 mb-4">
        <span class="text-muted fw-light">Email Client /</span> View Email
    </h4>

    @if(session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger">
            {{ session('error') }}
        </div>
    @endif

    @if(isset($error))
        <div class="alert alert-danger">
            <h5>Error</h5>
            <p>{{ $error }}</p>
            <a href="{{ route('admin.oauth.microsoft') }}" class="btn btn-primary">Connect to Microsoft</a>
        </div>
    @endif

    <div class="row">
        <!-- Email Sidebar -->
        <div class="col-md-3 col-sm-12 email-sidebar">
            <div class="card">
                <div class="card-body">
                    <a href="{{ route('admin.emails.index') }}" class="btn btn-primary w-100 mb-3">
                        <i class="ti ti-mail me-1"></i> Inbox
                    </a>

                    <h6 class="text-uppercase mt-4 mb-2">Folders</h6>
                    <ul class="list-group list-group-flush">
                        @foreach($folders ?? [] as $folder)
                            <li class="list-group-item d-flex justify-content-between align-items-center p-2">
                                <a href="{{ route('admin.emails.folder', $folder['id']) }}" class="text-body d-block w-100">
                                    {{ $folder['displayName'] }}
                                    @if(isset($folder['unreadItemCount']) && $folder['unreadItemCount'] > 0)
                                        <span class="badge bg-primary rounded-pill float-end">{{ $folder['unreadItemCount'] }}</span>
                                    @endif
                                </a>
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>
        <!-- / Email Sidebar -->

        <!-- Email View -->
        <div class="col-md-9 col-sm-12 email-view">
            @if(isset($email))
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">{{ $email['subject'] ?? 'No Subject' }}</h5>
                            <small class="text-muted">
                                {{ \Carbon\Carbon::parse($email['receivedDateTime'])->format('F d, Y H:i') }}
                            </small>
                        </div>
                        <div>
                            <div class="btn-group">
                                @if(isset($email['internetMessageId']))
                                    <a href="{{ route('admin.emails.thread', $email['id']) }}" class="btn btn-outline-primary btn-sm" title="View Thread">
                                        <i class="ti ti-messages"></i> View Thread
                                    </a>
                                @endif
                                <form action="{{ route('admin.emails.mark-as-unread', $email['id']) }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-outline-secondary btn-sm" title="Mark as Unread">
                                        <i class="ti ti-mail"></i>
                                    </button>
                                </form>
                                <a href="{{ route('admin.emails.index') }}" class="btn btn-outline-secondary btn-sm" title="Back to Inbox">
                                    <i class="ti ti-arrow-back"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="email-header mb-4">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0 me-3">
                                    @if(isset($email['from']['emailAddress']['address']))
                                        <div class="avatar">
                                            <span class="avatar-initial rounded-circle bg-primary">
                                                {{ strtoupper(substr($email['from']['emailAddress']['address'], 0, 1)) }}
                                            </span>
                                        </div>
                                    @endif
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">
                                        {{ $email['from']['emailAddress']['name'] ?? $email['from']['emailAddress']['address'] ?? 'Unknown Sender' }}
                                    </h6>
                                    <small class="text-muted">
                                        {{ $email['from']['emailAddress']['address'] ?? '' }}
                                    </small>
                                </div>
                            </div>

                            @if(isset($email['toRecipients']) && count($email['toRecipients']) > 0)
                                <div class="mt-2">
                                    <strong>To:</strong>
                                    @foreach($email['toRecipients'] as $recipient)
                                        {{ $recipient['emailAddress']['name'] ?? $recipient['emailAddress']['address'] }}@if(!$loop->last), @endif
                                    @endforeach
                                </div>
                            @endif

                            @if(isset($email['ccRecipients']) && count($email['ccRecipients']) > 0)
                                <div class="mt-1">
                                    <strong>CC:</strong>
                                    @foreach($email['ccRecipients'] as $recipient)
                                        {{ $recipient['emailAddress']['name'] ?? $recipient['emailAddress']['address'] }}@if(!$loop->last), @endif
                                    @endforeach
                                </div>
                            @endif
                        </div>

                        <!-- Email Internal Parameters -->
                        <div class="mt-3">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Email Internal Parameters</h6>
                                </div>
                                <div class="card-body p-3">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-2">
                                                <strong>Internet Message ID:</strong>
                                                <span class="text-muted">{{ $email['internetMessageId'] ?? 'Not available' }}</span>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Conversation ID:</strong>
                                                <span class="text-muted">{{ $email['conversationId'] ?? 'Not available' }}</span>
                                            </div>
                                            <div class="mb-2">
                                                <strong>In-Reply-To:</strong>
                                                <span class="text-muted">
                                                    @php
                                                        $inReplyTo = 'Not available';
                                                        if(isset($email['internetMessageHeaders']) && count($email['internetMessageHeaders']) > 0) {
                                                            foreach($email['internetMessageHeaders'] as $header) {
                                                                if(strtolower($header['name']) === 'in-reply-to') {
                                                                    $inReplyTo = $header['value'];
                                                                    break;
                                                                }
                                                            }
                                                        }
                                                    @endphp
                                                    {{ $inReplyTo }}
                                                </span>
                                            </div>
                                            <div class="mb-2">
                                                <strong>References:</strong>
                                                <span class="text-muted">
                                                    @php
                                                        $references = 'Not available';
                                                        if(isset($email['internetMessageHeaders']) && count($email['internetMessageHeaders']) > 0) {
                                                            foreach($email['internetMessageHeaders'] as $header) {
                                                                if(strtolower($header['name']) === 'references') {
                                                                    $references = $header['value'];
                                                                    break;
                                                                }
                                                            }
                                                        }
                                                    @endphp
                                                    {{ $references }}
                                                </span>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Created:</strong>
                                                <span class="text-muted">{{ isset($email['createdDateTime']) ? \Carbon\Carbon::parse($email['createdDateTime'])->format('F d, Y H:i') : 'Not available' }}</span>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Last Modified:</strong>
                                                <span class="text-muted">{{ isset($email['lastModifiedDateTime']) ? \Carbon\Carbon::parse($email['lastModifiedDateTime'])->format('F d, Y H:i') : 'Not available' }}</span>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Sent:</strong>
                                                <span class="text-muted">{{ isset($email['sentDateTime']) ? \Carbon\Carbon::parse($email['sentDateTime'])->format('F d, Y H:i') : 'Not available' }}</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-2">
                                                <strong>Is Draft:</strong>
                                                <span class="text-muted">{{ isset($email['isDraft']) ? ($email['isDraft'] ? 'Yes' : 'No') : 'Not available' }}</span>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Categories:</strong>
                                                <span class="text-muted">
                                                    @if(isset($email['categories']) && count($email['categories']) > 0)
                                                        @foreach($email['categories'] as $category)
                                                            <span class="badge bg-info me-1">{{ $category }}</span>
                                                        @endforeach
                                                    @else
                                                        None
                                                    @endif
                                                </span>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Flag Status:</strong>
                                                <span class="text-muted">{{ isset($email['flag']['flagStatus']) ? ucfirst($email['flag']['flagStatus']) : 'Not flagged' }}</span>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Importance:</strong>
                                                <span class="text-muted">{{ isset($email['importance']) ? ucfirst($email['importance']) : 'Normal' }}</span>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Web Link:</strong>
                                                @if(isset($email['webLink']))
                                                    <a href="{{ $email['webLink'] }}" target="_blank" class="text-primary">Open in Outlook <i class="ti ti-external-link"></i></a>
                                                @else
                                                    <span class="text-muted">Not available</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>

                                    @if(isset($email['internetMessageHeaders']) && count($email['internetMessageHeaders']) > 0)
                                        <hr>
                                        <div class="mt-3">
                                            <h6 class="mb-2">Message Headers</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm table-bordered">
                                                    <thead>
                                                        <tr>
                                                            <th>Name</th>
                                                            <th>Value</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach($email['internetMessageHeaders'] as $header)
                                                            <tr>
                                                                <td><strong>{{ $header['name'] }}</strong></td>
                                                                <td><span class="text-muted">{{ $header['value'] }}</span></td>
                                                            </tr>
                                                        @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <!-- / Email Internal Parameters -->

                        <hr>

                        <div class="email-content mt-4">
                            @if(isset($email['body']['content']))
                                <div class="email-body">
                                    {!! $email['body']['content'] !!}
                                </div>
                            @else
                                <p class="text-muted">No content</p>
                            @endif
                        </div>

                        @if(isset($email['attachments']) && count($email['attachments']) > 0)
                            <div class="email-attachments mt-4">
                                <h6>Attachments ({{ count($email['attachments']) }})</h6>
                                <div class="row">
                                    @foreach($email['attachments'] as $attachment)
                                        <div class="col-md-4 col-sm-6 mb-3">
                                            <div class="card">
                                                <div class="card-body p-2">
                                                    <div class="d-flex align-items-center">
                                                        <i class="ti ti-file me-2" style="font-size: 1.5rem;"></i>
                                                        <div>
                                                            <p class="mb-0 text-truncate" style="max-width: 150px;">{{ $attachment['name'] }}</p>
                                                            <small class="text-muted">{{ round($attachment['size'] / 1024) }} KB</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Reply Section -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Reply</h5>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-primary btn-sm" id="reply-btn" onclick="toggleReplyType('reply')">
                                Reply
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="reply-all-btn" onclick="toggleReplyType('replyAll')">
                                Reply All
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <form id="reply-form" action="{{ route('admin.emails.reply', $email['id']) }}" method="POST">
                            @csrf
                            <div class="mb-3">
                                <label for="content" class="form-label">Message</label>
                                <textarea id="content" name="content" class="form-control" rows="6" placeholder="Type your reply here..."></textarea>
                            </div>
                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="ti ti-send me-1"></i> Send Reply
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <!-- / Reply Section -->
            @else
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="ti ti-mail-off mb-2" style="font-size: 3rem;"></i>
                        <h5>Email not found</h5>
                        <p class="text-muted">The email you're looking for doesn't exist or you don't have permission to view it.</p>
                        <a href="{{ route('admin.emails.index') }}" class="btn btn-primary mt-2">
                            <i class="ti ti-arrow-back me-1"></i> Back to Inbox
                        </a>
                    </div>
                </div>
            @endif
        </div>
        <!-- / Email View -->
    </div>
</div>
@endsection

@section('page-script')
<script>
    function toggleReplyType(type) {
        const form = document.getElementById('reply-form');
        const replyBtn = document.getElementById('reply-btn');
        const replyAllBtn = document.getElementById('reply-all-btn');

        if (type === 'reply') {
            form.action = "{{ route('admin.emails.reply', $email['id'] ?? 0) }}";
            replyBtn.classList.remove('btn-outline-primary');
            replyBtn.classList.add('btn-primary');
            replyAllBtn.classList.remove('btn-primary');
            replyAllBtn.classList.add('btn-outline-secondary');
        } else {
            form.action = "{{ route('admin.emails.reply-all', $email['id'] ?? 0) }}";
            replyAllBtn.classList.remove('btn-outline-secondary');
            replyAllBtn.classList.add('btn-primary');
            replyBtn.classList.remove('btn-primary');
            replyBtn.classList.add('btn-outline-primary');
        }
    }
</script>
@endsection
