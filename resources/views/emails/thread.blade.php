@extends('layouts/layoutSavAdminMaster')

@section('title', 'Email Thread')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
    <h4 class="fw-bold py-3 mb-4">
        <span class="text-muted fw-light">Email Client / <a href="{{ route('admin.emails.index') }}">Inbox</a> /</span> Thread
    </h4>

    @if(isset($error))
        <div class="alert alert-danger">
            <h5>Error</h5>
            <p>{{ $error }}</p>
            <a href="{{ route('admin.oauth.microsoft') }}" class="btn btn-primary">Connect to Microsoft</a>
        </div>
    @endif

    <div class="row">
        <!-- Email Sidebar -->
        <div class="col-md-3 col-sm-12 email-sidebar">
            <div class="card">
                <div class="card-body">
                    <a href="{{ route('admin.emails.index') }}" class="btn btn-primary w-100 mb-3">
                        <i class="ti ti-mail me-1"></i> Inbox
                    </a>

                    <h6 class="text-uppercase mt-4 mb-2">Folders</h6>
                    <ul class="list-group list-group-flush">
                        @foreach($folders ?? [] as $folder)
                            <li class="list-group-item d-flex justify-content-between align-items-center p-2">
                                <a href="{{ route('admin.emails.folder', $folder['id']) }}" class="text-body d-block w-100">
                                    {{ $folder['displayName'] }}
                                    @if(isset($folder['unreadItemCount']) && $folder['unreadItemCount'] > 0)
                                        <span class="badge bg-primary rounded-pill float-end">{{ $folder['unreadItemCount'] }}</span>
                                    @endif
                                </a>
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>
        <!-- / Email Sidebar -->

        <!-- Email Thread View -->
        <div class="col-md-9 col-sm-12 email-thread-view">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title">Original Email</h5>
                    <div class="mt-2">
                        <a href="{{ route('admin.emails.view', $email['id']) }}" class="btn btn-outline-secondary btn-sm">
                            <i class="ti ti-arrow-back me-1"></i> Back to Email
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>From:</strong> {{ $email['from']['emailAddress']['name'] ?? '' }} ({{ $email['from']['emailAddress']['address'] ?? '' }})
                        </div>
                        <div class="col-md-6">
                            <strong>Received:</strong> {{ \Carbon\Carbon::parse($email['receivedDateTime'])->format('Y-m-d H:i:s') }}
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <strong>To:</strong> 
                            @foreach($email['toRecipients'] as $recipient)
                                {{ $recipient['emailAddress']['name'] ?? $recipient['emailAddress']['address'] }}{{ !$loop->last ? ', ' : '' }}
                            @endforeach
                        </div>
                    </div>
                    @if(isset($email['ccRecipients']) && count($email['ccRecipients']) > 0)
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <strong>CC:</strong> 
                                @foreach($email['ccRecipients'] as $recipient)
                                    {{ $recipient['emailAddress']['name'] ?? $recipient['emailAddress']['address'] }}{{ !$loop->last ? ', ' : '' }}
                                @endforeach
                            </div>
                        </div>
                    @endif
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <strong>Subject:</strong> {{ $email['subject'] }}
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <strong>Message ID:</strong> <small>{{ $email['internetMessageId'] ?? 'N/A' }}</small>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <strong>Content:</strong>
                            <div class="border p-3 mt-2">
                                {!! $email['body']['content'] !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            @if(count($threadMessages) > 0)
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Thread Messages ({{ count($threadMessages) }})</h5>
                        @if(isset($searchMethod))
                            <small class="text-muted">Search method: {{ $searchMethod }}</small>
                        @endif
                    </div>
                    <div class="card-body">
                        @foreach($threadMessages as $message)
                            <div class="email-thread-message mb-4 border-bottom pb-3">
                                <div class="row mb-2">
                                    <div class="col-md-6">
                                        <strong>From:</strong> {{ $message['sender']['emailAddress']['name'] ?? '' }} ({{ $message['sender']['emailAddress']['address'] ?? '' }})
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Received:</strong> {{ \Carbon\Carbon::parse($message['receivedDateTime'])->format('Y-m-d H:i:s') }}
                                        @if(isset($message['folderName']))
                                            <span class="badge bg-info">{{ $message['folderName'] }}</span>
                                        @endif
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-md-12">
                                        <strong>To:</strong> 
                                        @foreach($message['toRecipients'] as $recipient)
                                            {{ $recipient['emailAddress']['name'] ?? $recipient['emailAddress']['address'] }}{{ !$loop->last ? ', ' : '' }}
                                        @endforeach
                                    </div>
                                </div>
                                @if(isset($message['ccRecipients']) && count($message['ccRecipients']) > 0)
                                    <div class="row mb-2">
                                        <div class="col-md-12">
                                            <strong>CC:</strong> 
                                            @foreach($message['ccRecipients'] as $recipient)
                                                {{ $recipient['emailAddress']['name'] ?? $recipient['emailAddress']['address'] }}{{ !$loop->last ? ', ' : '' }}
                                            @endforeach
                                        </div>
                                    </div>
                                @endif
                                <div class="row mb-2">
                                    <div class="col-md-12">
                                        <strong>Subject:</strong> {{ $message['subject'] }}
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-md-12">
                                        <strong>Message ID:</strong> <small>{{ $message['internetMessageId'] ?? 'N/A' }}</small>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <strong>Content:</strong>
                                        <div class="border p-3 mt-2">
                                            {!! $message['body']['content'] !!}
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-12 text-end">
                                        <a href="{{ route('admin.emails.view', $message['id']) }}" class="btn btn-outline-primary btn-sm">
                                            <i class="ti ti-eye me-1"></i> View Email
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @else
                <div class="alert alert-info">
                    <p>No thread messages found. This could be because:</p>
                    <ul>
                        <li>There are no replies to this email yet</li>
                        <li>The messages might be in a different folder that we couldn't access</li>
                        <li>The internetMessageId might not be properly tracked</li>
                    </ul>
                    
                    <div class="mt-3">
                        <h6>Debugging Information:</h6>
                        <p><strong>Internet Message ID:</strong> {{ $email['internetMessageId'] ?? 'N/A' }}</p>
                        <p><strong>Search Method:</strong> {{ $searchMethod ?? 'None' }}</p>
                        @if(isset($error))
                            <p><strong>Error:</strong> {{ $error }}</p>
                        @endif
                    </div>
                </div>
            @endif
        </div>
        <!-- / Email Thread View -->
    </div>
</div>
@endsection