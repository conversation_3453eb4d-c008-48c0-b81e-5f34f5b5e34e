@extends('layouts/layoutSavAdminMaster')

@section('title', 'Email Client')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
    <h4 class="fw-bold py-3 mb-4">
        <span class="text-muted fw-light">Email Client /</span> {{ $currentFolderName ?? 'Inbox' }}
    </h4>

    @if(isset($error))
        <div class="alert alert-danger">
            <h5>Error</h5>
            <p>{{ $error }}</p>
            <a href="{{ route('admin.oauth.microsoft') }}" class="btn btn-primary">Connect to Microsoft</a>
        </div>
    @endif

    @if(isset($clientFiltered) && $clientFiltered)
        <div class="alert alert-info">
            <i class="ti ti-info-circle me-2"></i>
            Searching by Message ID is performed on the server. Pagination may be limited.
        </div>
    @endif

    <div class="row">
        <!-- <PERSON>ail Sidebar -->
        <div class="col-md-3 col-sm-12 email-sidebar">
            <div class="card">
                <div class="card-body">
                    <a href="{{ route('admin.emails.index') }}" class="btn btn-primary w-100 mb-3">
                        <i class="ti ti-mail me-1"></i> Inbox
                    </a>

                    <h6 class="text-uppercase mt-4 mb-2">Folders</h6>
                    <ul class="list-group list-group-flush">
                        @foreach($folders ?? [] as $folder)
                            <li class="list-group-item d-flex justify-content-between align-items-center p-2 {{ isset($currentFolder) && $currentFolder == $folder['id'] ? 'active' : '' }}">
                                <a href="{{ route('admin.emails.folder', $folder['id']) }}" class="text-body d-block w-100">
                                    {{ $folder['displayName'] }}
                                    @if(isset($folder['unreadItemCount']) && $folder['unreadItemCount'] > 0)
                                        <span class="badge bg-primary rounded-pill float-end">{{ $folder['unreadItemCount'] }}</span>
                                    @endif
                                </a>
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>
        <!-- / Email Sidebar -->

        <!-- Email List -->
        <div class="col-md-9 col-sm-12 email-list">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        {{ $currentFolderName ?? 'Inbox' }}
                        @if(isset($searchQuery) && $searchQuery)
                            <small class="text-muted">(Search results for: {{ $searchQuery }})</small>
                        @endif
                    </h5>
                    <div class="d-flex">
                        <div class="form-check me-3 d-flex align-items-center">
                            <input class="form-check-input" type="checkbox" id="show-conversations" {{ session('show_conversations', false) ? 'checked' : '' }}>
                            <label class="form-check-label ms-1" for="show-conversations">
                                Show as Conversations
                            </label>
                        </div>
                        <div class="input-group me-2">
                            <select class="form-select" id="search-field" style="max-width: 150px;">
                                <option value="subject">Subject</option>
                                <option value="internetMessageId">Message ID</option>
                                <option value="both">Both</option>
                            </select>
                            <input type="text" class="form-control" placeholder="Search emails..." id="email-search">
                            <button class="btn btn-outline-primary" type="button" id="search-button">
                                <i class="ti ti-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <tbody>
                                @if(isset($showConversations) && $showConversations)
                                    @forelse($conversations ?? [] as $conversation)
                                        <tr class="{{ $conversation['hasUnread'] ? 'fw-bold' : '' }}">
                                            <td class="py-3">
                                                <a href="{{ route('admin.emails.view', $conversation['latestEmail']['id']) }}" class="d-block text-body">
                                                    <div class="d-flex align-items-center">
                                                        <div class="flex-shrink-0 me-3">
                                                            @if(isset($conversation['latestEmail']['from']['emailAddress']['address']))
                                                                <div class="avatar">
                                                                    <span class="avatar-initial rounded-circle bg-primary">
                                                                        {{ strtoupper(substr($conversation['latestEmail']['from']['emailAddress']['address'], 0, 1)) }}
                                                                    </span>
                                                                </div>
                                                            @endif
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <h6 class="mb-0">
                                                                    {{ $conversation['latestEmail']['from']['emailAddress']['name'] ?? $conversation['latestEmail']['from']['emailAddress']['address'] ?? 'Unknown Sender' }}
                                                                    @if($conversation['count'] > 1)
                                                                        <span class="badge bg-secondary ms-1">{{ $conversation['count'] }}</span>
                                                                    @endif
                                                                </h6>
                                                                <small class="text-muted">
                                                                    {{ \Carbon\Carbon::parse($conversation['latestEmail']['receivedDateTime'])->format('M d, H:i') }}
                                                                </small>
                                                            </div>
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <p class="mb-0 text-truncate" style="max-width: 500px;">
                                                                    <span class="fw-bold">{{ $conversation['latestEmail']['subject'] ?? 'No Subject' }}</span>
                                                                    - {{ $conversation['latestEmail']['bodyPreview'] ?? '' }}
                                                                    @if(count($conversation['participants']) > 1)
                                                                        <small class="text-muted d-block">
                                                                            Participants: {{ implode(', ', array_slice($conversation['participants'], 0, 3)) }}
                                                                            @if(count($conversation['participants']) > 3)
                                                                                and {{ count($conversation['participants']) - 3 }} more
                                                                            @endif
                                                                        </small>
                                                                    @endif
                                                                </p>
                                                                <div>
                                                                    @if(isset($conversation['latestEmail']['hasAttachments']) && $conversation['latestEmail']['hasAttachments'])
                                                                        <i class="ti ti-paperclip text-muted"></i>
                                                                    @endif
                                                                    @if(isset($conversation['latestEmail']['importance']) && $conversation['latestEmail']['importance'] === 'high')
                                                                        <i class="ti ti-alert-triangle text-danger ms-1"></i>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </a>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td class="text-center py-4">
                                                <div class="d-flex flex-column align-items-center">
                                                    <i class="ti ti-inbox mb-2" style="font-size: 3rem;"></i>
                                                    <h5>No emails found</h5>
                                                    <p class="text-muted">This folder is empty</p>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                @else
                                    @forelse($emails ?? [] as $email)
                                        <tr class="{{ isset($email['isRead']) && !$email['isRead'] ? 'fw-bold' : '' }}">
                                            <td class="py-3">
                                                <a href="{{ route('admin.emails.view', $email['id']) }}" class="d-block text-body">
                                                    <div class="d-flex align-items-center">
                                                        <div class="flex-shrink-0 me-3">
                                                            @if(isset($email['from']['emailAddress']['address']))
                                                                <div class="avatar">
                                                                    <span class="avatar-initial rounded-circle bg-primary">
                                                                        {{ strtoupper(substr($email['from']['emailAddress']['address'], 0, 1)) }}
                                                                    </span>
                                                                </div>
                                                            @endif
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <h6 class="mb-0">
                                                                    {{ $email['from']['emailAddress']['name'] ?? $email['from']['emailAddress']['address'] ?? 'Unknown Sender' }}
                                                                </h6>
                                                                <small class="text-muted">
                                                                    {{ \Carbon\Carbon::parse($email['receivedDateTime'])->format('M d, H:i') }}
                                                                </small>
                                                            </div>
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <p class="mb-0 text-truncate" style="max-width: 500px;">
                                                                    <span class="fw-bold">{{ $email['subject'] ?? 'No Subject' }}</span>
                                                                    - {{ $email['bodyPreview'] ?? '' }}
                                                                    @if(isset($email['internetMessageId']))
                                                                        <small class="text-muted d-block">ID: {{ $email['internetMessageId'] }}</small>
                                                                    @endif
                                                                </p>
                                                                <div>
                                                                    @if(isset($email['hasAttachments']) && $email['hasAttachments'])
                                                                        <i class="ti ti-paperclip text-muted"></i>
                                                                    @endif
                                                                    @if(isset($email['importance']) && $email['importance'] === 'high')
                                                                        <i class="ti ti-alert-triangle text-danger ms-1"></i>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </a>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td class="text-center py-4">
                                                <div class="d-flex flex-column align-items-center">
                                                    <i class="ti ti-inbox mb-2" style="font-size: 3rem;"></i>
                                                    <h5>No emails found</h5>
                                                    <p class="text-muted">This folder is empty</p>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            @if(count($emails ?? []) > 0)
                                <small class="text-muted">Showing {{ count($emails) }} emails</small>
                            @endif
                        </div>
                        <div>
                            <nav aria-label="Page navigation">
                                <ul class="pagination pagination-sm mb-0">
                                    @if(isset($page) && $page > 1)
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url()->current() }}?page={{ $page - 1 }}&pageSize={{ $pageSize ?? 10 }}{{ isset($filter) ? '&filter='.urlencode($filter) : '' }}" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    @endif

                                    @if(isset($page) && isset($totalPages))
                                        @for($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++)
                                            <li class="page-item {{ $i == $page ? 'active' : '' }}">
                                                <a class="page-link" href="{{ url()->current() }}?page={{ $i }}&pageSize={{ $pageSize ?? 10 }}{{ isset($filter) ? '&filter='.urlencode($filter) : '' }}">{{ $i }}</a>
                                            </li>
                                        @endfor
                                    @endif

                                    @if(isset($page) && isset($totalPages) && $page < $totalPages)
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url()->current() }}?page={{ $page + 1 }}&pageSize={{ $pageSize ?? 10 }}{{ isset($filter) ? '&filter='.urlencode($filter) : '' }}" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                    @elseif(isset($nextLink) && $nextLink)
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url()->current() }}?page={{ $page ?? 1 + 1 }}&pageSize={{ $pageSize ?? 10 }}{{ isset($filter) ? '&filter='.urlencode($filter) : '' }}&nextLink={{ urlencode($nextLink) }}" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                    @endif
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- / Email List -->
    </div>
</div>
@endsection

@section('page-script')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Email search functionality
        const searchInput = document.getElementById('email-search');
        const searchButton = document.getElementById('search-button');
        const searchField = document.getElementById('search-field');
        const showConversationsCheckbox = document.getElementById('show-conversations');

        // Handle conversation view toggle
        if (showConversationsCheckbox) {
            showConversationsCheckbox.addEventListener('change', function() {
                // Send AJAX request to save preference
                fetch('{{ route("admin.emails.toggle-conversation-view") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        show_conversations: this.checked
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload the page to apply the new view
                        window.location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        }

        // Existing search functionality
        if (searchInput && searchButton && searchField) {
            // Populează câmpul de căutare și selectorul de câmp cu valorile curente, dacă există
            const currentUrl = new URL(window.location.href);
            const currentFilter = currentUrl.searchParams.get('filter');
            const currentSearch = currentUrl.searchParams.get('search');

            if (currentSearch) {
                searchInput.value = currentSearch;
                // Presupunem că este o căutare de subiect dacă avem parametrul search
                searchField.value = 'subject';
            } else if (currentFilter) {
                // Verifică ce tip de filtru este aplicat
                if (currentFilter.includes('internetMessageId')) {
                    searchField.value = 'internetMessageId';
                    // Extrage termenul de căutare din filtru
                    const match = currentFilter.match(/contains\(internetMessageId,'([^']+)'\)/);
                    if (match && match[1]) {
                        searchInput.value = match[1];
                    }
                } else if (currentFilter.includes('subject') && currentFilter.includes('bodyPreview')) {
                    searchField.value = 'both';
                    // Extrage termenul de căutare din filtru
                    const match = currentFilter.match(/contains\(subject,'([^']+)'\)/);
                    if (match && match[1]) {
                        searchInput.value = match[1];
                    }
                } else if (currentFilter.includes('subject')) {
                    searchField.value = 'subject';
                    // Extrage termenul de căutare din filtru
                    const match = currentFilter.match(/contains\(subject,'([^']+)'\)/);
                    if (match && match[1]) {
                        searchInput.value = match[1];
                    }
                }
            }

            searchButton.addEventListener('click', function() {
                const searchTerm = searchInput.value.trim();
                if (searchTerm) {
                    const currentUrl = new URL(window.location.href);

                    // Șterge parametrii de paginare existenți
                    currentUrl.searchParams.delete('page');
                    currentUrl.searchParams.delete('nextLink');

                    // Construiește query-ul în funcție de câmpul selectat
                    switch (searchField.value) {
                        case 'internetMessageId':
                            // Pentru Message ID, continuăm să folosim filter
                            currentUrl.searchParams.delete('search');
                            currentUrl.searchParams.set('filter', `contains(internetMessageId,'${searchTerm}')`);
                            break;
                        case 'subject':
                            // Pentru subject, folosim parametrul search și indicăm că este o căutare de subiect
                            currentUrl.searchParams.delete('filter');
                            currentUrl.searchParams.set('search', searchTerm);
                            currentUrl.searchParams.set('searchType', 'subject');
                            break;
                        case 'both':
                        default:
                            // Pentru căutări combinate, folosim tot search
                            currentUrl.searchParams.delete('filter');
                            currentUrl.searchParams.set('search', searchTerm);
                            break;
                    }

                    // Reset page to 1 when searching
                    currentUrl.searchParams.set('page', 1);
                    window.location.href = currentUrl.toString();
                }
            });

            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchButton.click();
                }
            });
        }
    });
</script>
@endsection
