@extends('layouts/layoutSavAdminMaster')

@section('title', 'Hotel Actions')

@section('vendor-style')
@vite([
'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
'resources/assets/vendor/libs/select2/select2.scss',
'resources/assets/vendor/libs/@form-validation/form-validation.scss',
'resources/assets/vendor/libs/animate-css/animate.scss',
'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
'resources/assets/vendor/libs/quill/typography.scss',
'resources/assets/vendor/libs/quill/katex.scss',
'resources/assets/vendor/libs/quill/editor.scss'
])
@endsection

@section('vendor-script')
@vite([
'resources/assets/vendor/libs/jquery/jquery.js',
'resources/assets/vendor/libs/moment/moment.js',
'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
'resources/assets/vendor/libs/select2/select2.js',
'resources/assets/vendor/libs/@form-validation/popular.js',
'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
'resources/assets/vendor/libs/@form-validation/auto-focus.js',
'resources/assets/vendor/libs/cleavejs/cleave.js',
'resources/assets/vendor/libs/cleavejs/cleave-phone.js',
'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
'resources/assets/vendor/libs/quill/katex.js',
'resources/assets/vendor/libs/quill/quill.js',
'resources/js/email-wizard.js'
])
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Selected Hotels ({{ count($hotels) }})</h5>
                <div>
                    <a href="{{ route('admin.hotels.map', isset($filterParams) ? $filterParams : []) }}" class="btn btn-secondary">
                        <i class="ti ti-arrow-left me-1"></i> Back to Map
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-5">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Export Options</h5>
                            </div>
                            <div class="card-body">
                                <div>
                                    <button id="configureExportBtn" class="btn btn-primary mb-2 w-100">
                                        <i class="ti ti-settings me-1"></i> Configure Export Fields
                                    </button>
                                    <div id="selectedFieldsBadges" class="mb-2">
                                        <!-- Selected fields will be displayed here as badges -->
                                    </div>
                                    <div class="d-flex justify-content-between gap-2">
                                        <button id="exportExcelBtn" class="btn btn-success" style="width: 32%;">
                                            <i class="ti ti-file-spreadsheet me-1"></i> Export to Excel
                                        </button>
                                        <button id="exportPdfBtn" class="btn btn-danger" style="width: 32%;">
                                            <i class="ti ti-file-text me-1"></i> Export to PDF
                                        </button>
                                        <button id="exportCsvBtn" class="btn btn-info" style="width: 32%;">
                                            <i class="ti ti-file-csv me-1"></i> Export to CSV
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-7">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Email Options</h5>
                            </div>
                            <div class="card-body">
                                <form id="emailForm">
                                    <!-- Hidden inputs for JavaScript -->
                                    <input type="hidden" id="firstHotelId" value="{{ $hotels->first()->id ?? '' }}">
                                    <input type="hidden" id="hotelIds" value="{{ json_encode($hotels->pluck('id')) }}">
                                    <input type="hidden" name="_token" value="{{ csrf_token() }}">

                                    <!-- Wizard Navigation -->
                                    <ul class="nav nav-pills mb-3" id="emailWizardTabs" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active text-start" id="step1-tab" data-bs-toggle="pill" data-bs-target="#step1" type="button" role="tab" aria-controls="step1" aria-selected="true">
                                                <span class="d-none d-md-block">Step 1<br><small class="fst-italic fw-normal">Email Subject and Template</small></span>
                                                <span class="d-block d-md-none">1</span>
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link text-start" id="step2-tab" data-bs-toggle="pill" data-bs-target="#step2" type="button" role="tab" aria-controls="step2" aria-selected="false">
                                                <span class="d-none d-md-block">Step 2<br><small class="fst-italic fw-normal">Custom Variables</small></span>
                                                <span class="d-block d-md-none">2</span>
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link text-start" id="step3-tab" data-bs-toggle="pill" data-bs-target="#step3" type="button" role="tab" aria-controls="step3" aria-selected="false">
                                                <span class="d-none d-md-block">Step 3<br><small class="fst-italic fw-normal">Email Template Editing</small></span>
                                                <span class="d-block d-md-none">3</span>
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link text-start" id="step4-tab" data-bs-toggle="pill" data-bs-target="#step4" type="button" role="tab" aria-controls="step4" aria-selected="false">
                                                <span class="d-none d-md-block">Step 4<br><small class="fst-italic fw-normal">Email Preview</small></span>
                                                <span class="d-block d-md-none">4</span>
                                            </button>
                                        </li>
                                    </ul>

                                    <!-- Wizard Content -->
                                    <div class="tab-content" id="emailWizardContent">
                                        <!-- Step 1: Email Subject and Template -->
                                        <div class="tab-pane fade show active" id="step1" role="tabpanel" aria-labelledby="step1-tab">
                                            <h6 class="mb-1">Email Subject and Template</h6>
                                            <p class="text-muted mb-3">Select the email subject and template</p>

                                            <!-- Microsoft OAuth Token Status for Step 1 -->
                                            <div id="microsoftTokenStatus1" class="mb-3">
                                                <!-- Token status will be displayed here -->
                                            </div>

                                            <div class="mb-3">
                                                <label for="emailSubject" class="form-label">Email Subject</label>
                                                <input type="text" class="form-control" id="emailSubject" placeholder="Enter email subject">
                                            </div>
                                            <div class="mb-3">
                                                <label for="emailTemplate" class="form-label">Email Template</label>
                                                <select class="form-select" id="emailTemplate">
                                                    <option value="">Select a template</option>
                                                    @if(isset($emailTemplates))
                                                        @foreach($emailTemplates as $category => $templates)
                                                            <optgroup label="{{ \App\Enums\EmailTemplateCategoryEnum::from($category)->label() }}">
                                                                @foreach($templates as $template)
                                                                    <option value="{{ $template->id }}">{{ $template->name }}</option>
                                                                @endforeach
                                                            </optgroup>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>

                                            <div class="d-flex justify-content-end">
                                                <button type="button" class="btn btn-primary next-step" data-next="step2">
                                                    Next <i class="ti ti-arrow-right ms-1"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Step 2: Custom Variables -->
                                        <div class="tab-pane fade" id="step2" role="tabpanel" aria-labelledby="step2-tab">
                                            <h6 class="mb-1">Custom Variables</h6>
                                            <p class="text-muted mb-3">Set values for custom variables</p>

                                            <!-- Microsoft OAuth Token Status for Step 2 -->
                                            <div id="microsoftTokenStatus2" class="mb-3">
                                                <!-- Token status will be displayed here -->
                                            </div>

                                            <div id="customVariablesContainer" class="mb-3">
                                                <!-- Custom variables will be added here dynamically -->
                                                <div class="alert alert-info">
                                                    Please select an email template first to see custom variables.
                                                </div>
                                            </div>

                                            <!-- Action selection (initially visible) -->
                                            <div id="actionSelectionContainer" class="mb-3" style="display: block;">
                                                <label for="actionSelect" class="form-label">Select Action</label>
                                                <select class="form-select" id="actionSelect" name="action_id">
                                                    <option value="">Select an action</option>
                                                    @foreach($actions as $action)
                                                        <option value="{{ $action->id }}">{{ $action->name }} ({{ $action->start_date->format('d.m.Y') }} - {{ $action->end_date->format('d.m.Y') }})</option>
                                                    @endforeach
                                                </select>
                                                <div class="form-text">Select an action to associate with this email.</div>
                                            </div>

                                            <script>
                                                document.addEventListener('DOMContentLoaded', function() {
                                                    // Get the email template select element
                                                    const emailTemplateSelect = document.getElementById('emailTemplate');
                                                    console.log('Email template select found:', emailTemplateSelect);
                                                    const actionSelectionContainer = document.getElementById('actionSelectionContainer');
                                                    console.log('Action selection container found:', actionSelectionContainer);

                                                    // Check if the action selection container exists
                                                    if (!actionSelectionContainer) {
                                                        console.error('Action selection container not found!');
                                                    } else {
                                                        console.log('Action selection container display style:', actionSelectionContainer.style.display);
                                                        console.log('Action selection container computed style:', window.getComputedStyle(actionSelectionContainer).display);
                                                    }

                                                    // Add event listener to the email template select
                                                    emailTemplateSelect.addEventListener('change', function() {
                                                        console.log('Email template changed');
                                                        // Get the selected option
                                                        const selectedOption = this.options[this.selectedIndex];
                                                        console.log('Selected option:', selectedOption);

                                                        // Check if an option is selected
                                                        if (selectedOption) {
                                                            // Get the optgroup label of the selected option
                                                            const optgroupLabel = selectedOption.parentNode.getAttribute('label');
                                                            console.log('Selected template optgroup label:', optgroupLabel);
                                                            // Log the HTML content to check for hidden characters
                                                            console.log('Optgroup HTML:', selectedOption.parentNode.outerHTML);

                                                            // Show action selection if the template is from "Hotel Accommodation" group
                                                            // Use a more flexible check that looks for "hotel" and "accommod" in the label
                                                            const labelLower = optgroupLabel ? optgroupLabel.toLowerCase() : '';
                                                            if (labelLower.includes('hotel') && 
                                                                (labelLower.includes('accommod') || labelLower.includes('accomod'))) {
                                                                console.log('Showing action selection container');
                                                                actionSelectionContainer.style.display = 'block';
                                                                // Verify the display style was set
                                                                console.log('Action selection container display style after setting to block:', actionSelectionContainer.style.display);
                                                                console.log('Action selection container computed style after setting to block:', window.getComputedStyle(actionSelectionContainer).display);
                                                            } else {
                                                                console.log('Hiding action selection container');
                                                                actionSelectionContainer.style.display = 'none';
                                                                // Reset the action select value
                                                                document.getElementById('actionSelect').value = '';
                                                            }
                                                        } else {
                                                            // Hide action selection if no template is selected
                                                            actionSelectionContainer.style.display = 'none';
                                                            // Reset the action select value
                                                            document.getElementById('actionSelect').value = '';
                                                        }
                                                    });
                                                });
                                            </script>

                                            <div class="alert alert-secondary mb-3">
                                                <i class="ti ti-info-circle me-1"></i>
                                                Common variables (like hotel name, address, etc.) will be automatically filled from the database.
                                            </div>


                                            <div class="d-flex justify-content-between">
                                                <button type="button" class="btn btn-secondary prev-step" data-prev="step1">
                                                    <i class="ti ti-arrow-left me-1"></i> Previous
                                                </button>
                                                <button type="button" class="btn btn-primary next-step" data-next="step3">
                                                    Next <i class="ti ti-arrow-right ms-1"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Step 3: Email Template Editing -->
                                        <div class="tab-pane fade" id="step3" role="tabpanel" aria-labelledby="step3-tab">
                                            <h6 class="mb-1">Email Template Editing</h6>
                                            <p class="text-muted mb-3">Edit the email template content</p>

                                            <!-- Microsoft OAuth Token Status for Step 3 -->
                                            <div id="microsoftTokenStatus3" class="mb-3">
                                                <!-- Token status will be displayed here -->
                                            </div>

                                            <div class="mb-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <label for="emailContent" class="form-label mb-0">Email Content</label>
                                                    <!-- HTML Source option removed as requested -->
                                                    <div class="btn-group" role="group" style="display: none;">
                                                        <input type="radio" class="btn-check" name="editorMode" id="htmlMode" value="html" checked>
                                                        <label class="btn btn-outline-primary btn-sm" for="htmlMode">HTML</label>
                                                    </div>
                                                </div>
                                                <!-- Quill editor container for HTML mode -->
                                                <div id="quill-editor" style="height: 300px; border: 1px solid #d8d6de; border-radius: 0.375rem;"></div>

                                                <!-- Plain text editor for Text mode (initially hidden) -->
                                                <textarea class="form-control" id="emailContent" rows="10" style="display: none;"></textarea>

                                                <!-- Add some CSS to ensure Quill editor is visible -->
                                                <style>
                                                    .ql-toolbar.ql-snow {
                                                        border: 1px solid #d8d6de;
                                                        border-top-left-radius: 0.375rem;
                                                        border-top-right-radius: 0.375rem;
                                                        background-color: #f8f8f8;
                                                        padding: 8px;
                                                    }
                                                    .ql-container.ql-snow {
                                                        border: 1px solid #d8d6de;
                                                        border-bottom-left-radius: 0.375rem;
                                                        border-bottom-right-radius: 0.375rem;
                                                        min-height: 200px;
                                                    }
                                                    .ql-editor {
                                                        min-height: 200px;
                                                        background-color: #fff;
                                                        font-family: 'Helvetica Neue', Arial, sans-serif;
                                                        font-size: 14px;
                                                        line-height: 1.5;
                                                        padding: 12px 15px;
                                                    }
                                                </style>
                                            </div>

                                            <div class="d-flex justify-content-between">
                                                <button type="button" class="btn btn-secondary prev-step" data-prev="step2">
                                                    <i class="ti ti-arrow-left me-1"></i> Previous
                                                </button>
                                                <button type="button" class="btn btn-primary next-step" data-next="step4">
                                                    Next <i class="ti ti-arrow-right ms-1"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Step 4: Email Preview -->
                                        <div class="tab-pane fade" id="step4" role="tabpanel" aria-labelledby="step4-tab">
                                            <h6 class="mb-1">Email Preview</h6>
                                            <p class="text-muted mb-3">Preview the email before sending</p>

                                            <div class="card mb-3">
                                                <div class="card-header">
                                                    <h6 class="mb-0">Preview</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-2">
                                                        <strong>Subject:</strong> <span id="previewSubject"></span>
                                                    </div>
                                                    <div>
                                                        <strong>Content:</strong>
                                                        <div id="previewContent" class="border p-3 mt-2"></div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Microsoft OAuth Token Status -->
                                            <div id="microsoftTokenStatus">
                                                <!-- Token status will be displayed here -->
                                            </div>

                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="confirmSend">
                                                <label class="form-check-label" for="confirmSend">
                                                    I have reviewed the email and confirm that I want to send it to {{ count($hotels) }} hotel(s).
                                                </label>
                                            </div>

                                            <div class="d-flex justify-content-between">
                                                <button type="button" class="btn btn-secondary prev-step" data-prev="step3">
                                                    <i class="ti ti-arrow-left me-1"></i> Previous
                                                </button>
                                                <button type="button" id="sendEmailBtn" class="btn btn-primary" disabled>
                                                    <i class="ti ti-mail me-1"></i> Send Email
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Address</th>
                                <th>City</th>
                                <th>Partner Type</th>
                                <th>Stars</th>
                                <th>Price</th>
                                <th>Email</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($hotels as $hotel)
                            <tr>
                                <td><a href="{{ route('hotel.show', $hotel->id) }}" target="_blank">{{ $hotel->id }}</a></td>
                                <td>{{ $hotel->name }}</td>
                                <td>{{ $hotel->full_address }}</td>
                                <td>{{ $hotel->city ? $hotel->city->name : 'N/A' }}</td>
                                <td>
                                    @if($hotel->partner_type)
                                    <span class="badge bg-{{ $hotel->partner_type === 'partner' ? 'success' : ($hotel->partner_type === 'prospect' ? 'warning' : ($hotel->partner_type === 'pending_partner' ? 'info' : 'dark')) }}">
                                        {{ $hotel->partner_type }}
                                    </span>
                                    @else
                                    N/A
                                    @endif
                                </td>
                                <td>{{ $hotel->stars ? str_repeat('★', $hotel->stars) : 'N/A' }}</td>
                                <td>{{ $hotel->recent_avg_price_per_person ? '€' . $hotel->recent_avg_price_per_person : 'N/A' }}</td>
                                <td>
                                    @if($hotel->email)
                                    {{ $hotel->email }}
                                    @elseif($hotel->contacts && $hotel->contacts->count() > 0)
                                        @php
                                            $defaultContacts = $hotel->contacts->where('default', 1);
                                        @endphp
                                        @if($defaultContacts->count() > 0)
                                            @foreach($defaultContacts as $contact)
                                                {{ $contact->email }}<br>
                                            @endforeach
                                        @else
                                            N/A
                                        @endif
                                    @else
                                    N/A
                                    @endif
                                </td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <a href="{{ route('hotel.edit', $hotel->id) }}" class="btn btn-sm btn-primary" title="Edit Hotel">
                                            <i class="ti ti-edit"></i>
                                        </a>
                                        <a href="{{ route('hotel.show', $hotel->id) }}" class="btn btn-sm btn-info" title="View Hotel Details">
                                            <i class="ti ti-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-secondary email-preview-btn" 
                                                data-hotel-id="{{ $hotel->id }}" title="Email Preview">
                                            <i class="ti ti-mail"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email wizard functionality is now in resources/js/email-wizard.js -->

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Configure Export Fields button
        document.getElementById('configureExportBtn').addEventListener('click', function() {
            $('#exportFieldsModal').modal('show');
        });

        // Initialize export fields
        const exportFields = [
            { id: 'id', name: 'ID', selected: true },
            { id: 'name', name: 'Name', selected: true },
            { id: 'breakfast', name: 'Breakfast', selected: true },
            { id: 'accept_groups', name: 'Accept Groups', selected: true },
            { id: 'capacity_rooms', name: 'Capacity Rooms', selected: true },
            { id: 'capacity_guests', name: 'Capacity Guests', selected: true },
            { id: 'full_address', name: 'Address', selected: true },
            { id: 'city', name: 'City', selected: true },
            { id: 'country', name: 'Country', selected: true },
            { id: 'level_1_division', name: 'Level 1 Division (Region)', selected: true },
            { id: 'level_2_division', name: 'Level 2 Division (Sub-Region)', selected: true },
            { id: 'hotel_brand', name: 'Hotel Brand', selected: true },
            { id: 'hotel_chain', name: 'Hotel Chain', selected: true },
            { id: 'hotel_market', name: 'Hotel Market', selected: true },
            { id: 'partner_type', name: 'Partner Type', selected: true },
            { id: 'stars', name: 'Stars', selected: true },
            { id: 'price', name: 'Price', selected: true },
            { id: 'emails', name: 'Emails', selected: true },
            { id: 'phones', name: 'Phones', selected: true },
            { id: 'distance_to_city_center', name: 'Distance to City Center', selected: true }
        ];

        // Load saved preferences from localStorage if available
        const savedPreferences = localStorage.getItem('hotelExportFieldsPreferences');
        if (savedPreferences) {
            try {
                const preferences = JSON.parse(savedPreferences);
                // Update the selected property of each field based on saved preferences
                preferences.forEach(pref => {
                    const field = exportFields.find(f => f.id === pref.id);
                    if (field) {
                        field.selected = pref.selected;
                    }
                });
            } catch (e) {
                console.error('Error loading saved export field preferences:', e);
            }
        }

        // Populate checkboxes in the modal
        const checkboxContainer = document.getElementById('exportFieldsCheckboxes');
        exportFields.forEach(field => {
            const checkboxDiv = document.createElement('div');
            checkboxDiv.className = 'form-check';
            checkboxDiv.innerHTML = 
                '<input class="form-check-input export-field-checkbox" type="checkbox" id="field_' + field.id + 
                '" data-field-id="' + field.id + '" ' + (field.selected ? 'checked' : '') + '>' +
                '<label class="form-check-label" for="field_' + field.id + '">' +
                field.name +
                '</label>';
            checkboxContainer.appendChild(checkboxDiv);
        });

        // Update badges function
        function updateFieldBadges() {
            const badgesContainer = document.getElementById('selectedFieldsBadges');
            badgesContainer.innerHTML = '';

            exportFields.forEach(field => {
                const badge = document.createElement('span');
                badge.className = 'badge me-1 mb-1 ' + (field.selected ? 'bg-primary' : 'bg-light text-dark');
                badge.textContent = field.name;
                badgesContainer.appendChild(badge);
            });
        }

        // Initialize badges
        updateFieldBadges();

        // Handle checkbox changes
        document.querySelectorAll('.export-field-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const fieldId = this.getAttribute('data-field-id');
                const field = exportFields.find(f => f.id === fieldId);
                if (field) {
                    field.selected = this.checked;
                    updateFieldBadges();
                }
            });
        });

        // Save export fields configuration
        document.getElementById('saveExportFieldsBtn').addEventListener('click', function() {
            // Save selected fields to localStorage
            localStorage.setItem('hotelExportFieldsPreferences', JSON.stringify(
                exportFields.map(field => ({
                    id: field.id,
                    selected: field.selected
                }))
            ));

            $('#exportFieldsModal').modal('hide');

            // Show a notification to the user
            Swal.fire({
                title: 'Preferences Saved',
                text: 'Your export field preferences have been saved and will be remembered the next time you visit this page.',
                icon: 'success',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });
        });

        // Update export buttons to use selected fields
        function getSelectedFields() {
            return exportFields.filter(field => field.selected).map(field => field.id);
        }

        document.getElementById('exportExcelBtn').addEventListener('click', function() {
            const selectedFields = getSelectedFields();
            if (selectedFields.length === 0) {
                Swal.fire({
                    title: 'Error',
                    text: 'Please select at least one field to export',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }

            // Create a form to submit the selected fields and hotel IDs
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ route('admin.hotels.export.excel') }}';
            form.style.display = 'none';

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            // Add selected fields
            const fieldsInput = document.createElement('input');
            fieldsInput.type = 'hidden';
            fieldsInput.name = 'fields';
            fieldsInput.value = JSON.stringify(selectedFields);
            form.appendChild(fieldsInput);

            // Add hotel IDs
            const hotelIdsInput = document.createElement('input');
            hotelIdsInput.type = 'hidden';
            hotelIdsInput.name = 'hotel_ids';
            hotelIdsInput.value = JSON.stringify({{ json_encode($hotels->pluck('id')) }});
            form.appendChild(hotelIdsInput);

            // Add city_id from filter parameters if available
            @if(isset($filterParams['city_id']))
            const cityIdInput = document.createElement('input');
            cityIdInput.type = 'hidden';
            cityIdInput.name = 'city_id';
            cityIdInput.value = '{{ $filterParams['city_id'] }}';
            form.appendChild(cityIdInput);
            @endif

            // Submit the form
            document.body.appendChild(form);
            form.submit();
        });

        document.getElementById('exportPdfBtn').addEventListener('click', function() {
            const selectedFields = getSelectedFields();
            if (selectedFields.length === 0) {
                Swal.fire({
                    title: 'Error',
                    text: 'Please select at least one field to export',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }

            // Create a form to submit the selected fields and hotel IDs
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ route('admin.hotels.export.pdf') }}';
            form.style.display = 'none';

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            // Add selected fields
            const fieldsInput = document.createElement('input');
            fieldsInput.type = 'hidden';
            fieldsInput.name = 'fields';
            fieldsInput.value = JSON.stringify(selectedFields);
            form.appendChild(fieldsInput);

            // Add hotel IDs
            const hotelIdsInput = document.createElement('input');
            hotelIdsInput.type = 'hidden';
            hotelIdsInput.name = 'hotel_ids';
            hotelIdsInput.value = JSON.stringify({{ json_encode($hotels->pluck('id')) }});
            form.appendChild(hotelIdsInput);

            // Add city_id from filter parameters if available
            @if(isset($filterParams['city_id']))
            const cityIdInput = document.createElement('input');
            cityIdInput.type = 'hidden';
            cityIdInput.name = 'city_id';
            cityIdInput.value = '{{ $filterParams['city_id'] }}';
            form.appendChild(cityIdInput);
            @endif

            // Submit the form
            document.body.appendChild(form);
            form.submit();
        });

        document.getElementById('exportCsvBtn').addEventListener('click', function() {
            const selectedFields = getSelectedFields();
            if (selectedFields.length === 0) {
                Swal.fire({
                    title: 'Error',
                    text: 'Please select at least one field to export',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }

            // Create a form to submit the selected fields and hotel IDs
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ route('admin.hotels.export.csv') }}';
            form.style.display = 'none';

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            // Add selected fields
            const fieldsInput = document.createElement('input');
            fieldsInput.type = 'hidden';
            fieldsInput.name = 'fields';
            fieldsInput.value = JSON.stringify(selectedFields);
            form.appendChild(fieldsInput);

            // Add hotel IDs
            const hotelIdsInput = document.createElement('input');
            hotelIdsInput.type = 'hidden';
            hotelIdsInput.name = 'hotel_ids';
            hotelIdsInput.value = JSON.stringify({{ json_encode($hotels->pluck('id')) }});
            form.appendChild(hotelIdsInput);

            // Add city_id from filter parameters if available
            @if(isset($filterParams['city_id']))
            const cityIdInput = document.createElement('input');
            cityIdInput.type = 'hidden';
            cityIdInput.name = 'city_id';
            cityIdInput.value = '{{ $filterParams['city_id'] }}';
            form.appendChild(cityIdInput);
            @endif

            // Submit the form
            document.body.appendChild(form);
            form.submit();
        });
    });
</script>

<!-- Export Fields Modal -->
<div class="modal fade" id="exportFieldsModal" tabindex="-1" aria-labelledby="exportFieldsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exportFieldsModalLabel">Configure Export Fields</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Select the fields you want to include in the export:</p>
                <div id="exportFieldsCheckboxes">
                    <!-- Checkboxes will be added here dynamically -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveExportFieldsBtn">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Email Preview Modal -->
<div class="modal fade" id="emailPreviewModal" tabindex="-1" aria-labelledby="emailPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailPreviewModalLabel">Email Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>To:</strong> <span id="previewEmailTo"></span>
                </div>
                <div class="mb-3">
                    <strong>Subject:</strong> <span id="previewEmailSubject"></span>
                </div>
                <div>
                    <strong>Content:</strong>
                    <div id="previewEmailContent" class="border p-3 mt-2"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Email Preview Button Click Handler
        document.querySelectorAll('.email-preview-btn').forEach(button => {
            button.addEventListener('click', function() {
                const hotelId = this.getAttribute('data-hotel-id');

                // Check if an email template is selected
                const templateId = document.getElementById('emailTemplate').value;
                if (!templateId) {
                    Swal.fire({
                        title: 'Template Required',
                        text: 'Please select an email template before previewing the email.',
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        showCancelButton: false,
                        showDenyButton: false,
                        buttonsStyling: false,
                        customClass: {
                            confirmButton: 'btn btn-primary'
                        }
                    });
                    return;
                }

                // Get the email subject and content
                const subject = document.getElementById('emailSubject').value;

                // Get the content from Step 4 preview instead of directly from the editor
                let content;
                // Try to get the content from the Step 4 preview
                const previewContentElement = document.getElementById('previewContent');
                if (previewContentElement && previewContentElement.innerHTML) {
                    content = previewContentElement.innerHTML;
                } 
                // Fallback to the editor content if Step 4 preview is not available
                else if (typeof window.htmlContent !== 'undefined' && window.htmlContent) {
                    content = window.htmlContent;
                } else if (typeof quill !== 'undefined' && quill) {
                    content = quill.root.innerHTML;
                } else {
                    content = document.getElementById('emailContent').value;
                }

                // Show loading indicator
                Swal.fire({
                    title: 'Generating Preview',
                    text: 'Please wait...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Extract variables from template content and subject
                function extractVariablesFromTemplate(content) {
                    const regex = new RegExp("\\{\\{([^\\}]+)\\}\\}", "g");
                    const variables = new Set();
                    let match;

                    while ((match = regex.exec(content)) !== null) {
                        variables.add(match[1]);
                    }

                    return Array.from(variables);
                }

                // Extract variables from content and subject
                const contentVariables = extractVariablesFromTemplate(content);
                const subjectVariables = extractVariablesFromTemplate(subject);
                const allTemplateVariables = [...new Set([...contentVariables, ...subjectVariables])];

                // If no variables found, no need to fetch data
                if (allTemplateVariables.length === 0) {
                    // Get custom variables
                    const customVariables = {};
                    document.querySelectorAll('.custom-variable-input').forEach(input => {
                        const varName = input.getAttribute('data-var-name');
                        customVariables[varName] = input.value;
                    });

                    // Update the modal with just custom variables
                    // Replace variables in subject
                    let previewSubject = subject;
                    for (const key in customVariables) {
                        if (customVariables.hasOwnProperty(key)) {
                            const value = customVariables[key];
                            const regex = new RegExp('\\{\\{' + key + '\\}\\}', 'g');
                            previewSubject = previewSubject.replace(regex, value || '[' + key + ']');
                        }
                    }

                    // Replace variables in content
                    let previewContent = content;
                    for (const key in customVariables) {
                        if (customVariables.hasOwnProperty(key)) {
                            const value = customVariables[key];
                            const regex = new RegExp('\\{\\{' + key + '\\}\\}', 'g');
                            previewContent = previewContent.replace(regex, value || '[' + key + ']');
                        }
                    }

                    // Update the modal
                    document.getElementById('previewEmailTo').textContent = 'N/A';
                    document.getElementById('previewEmailSubject').textContent = previewSubject;
                    document.getElementById('previewEmailContent').innerHTML = previewContent;

                    // Close loading indicator and show the modal
                    Swal.close();
                    const emailPreviewModal = new bootstrap.Modal(document.getElementById('emailPreviewModal'));
                    emailPreviewModal.show();
                    return;
                }

                // Fetch hotel data
                fetch('/admin/hotels/' + hotelId + '/variables')
                    .then(response => {
                        // Check if the response is ok (status in the range 200-299)
                        if (!response.ok) {
                            // If not ok, throw an error with the status and statusText
                            throw new Error(`Server responded with status: ${response.status} (${response.statusText})`);
                        }
                        try {
                            return response.json();
                        } catch (jsonError) {
                            throw new Error(`Failed to parse JSON response: ${jsonError.message}`);
                        }
                    })
                    .then(data => {
                        // Check if data is valid
                        if (!data) {
                            throw new Error('Server returned empty data');
                        }

                        // Log the data for debugging
                        console.log('Hotel variables data:', data);

                        // Get custom variables
                        const customVariables = {};
                        document.querySelectorAll('.custom-variable-input').forEach(input => {
                            const varName = input.getAttribute('data-var-name');
                            customVariables[varName] = input.value;
                        });

                        // Filter the data to only include variables that are present in the template
                        const filteredData = {};
                        for (const key in data) {
                            if (data.hasOwnProperty(key) && allTemplateVariables.includes(key)) {
                                filteredData[key] = data[key];
                            }
                        }

                        // Merge custom variables and filtered common variables
                        const allVariables = Object.assign({}, customVariables, filteredData);

                        // Replace variables in subject
                        let previewSubject = subject;
                        for (const key in allVariables) {
                            if (allVariables.hasOwnProperty(key)) {
                                const value = allVariables[key];
                                const regex = new RegExp('\\{\\{' + key + '\\}\\}', 'g');
                                previewSubject = previewSubject.replace(regex, value || '[' + key + ']');
                            }
                        }

                        // Replace variables in content
                        let previewContent = content;
                        for (const key in allVariables) {
                            if (allVariables.hasOwnProperty(key)) {
                                const value = allVariables[key];
                                const regex = new RegExp('\\{\\{' + key + '\\}\\}', 'g');
                                previewContent = previewContent.replace(regex, value || '[' + key + ']');
                            }
                        }

                        // Get the email address
                        let emailTo = data.hotel_email || 'N/A';

                        // Check if we have the minimum required data for preview
                        if (!data.hotel_name) {
                            console.warn('Missing hotel_name in data:', data);
                        }

                        // Update the modal
                        document.getElementById('previewEmailTo').textContent = emailTo;
                        document.getElementById('previewEmailSubject').textContent = previewSubject;
                        document.getElementById('previewEmailContent').innerHTML = previewContent;

                        // Close loading indicator and show the modal
                        Swal.close();
                        const emailPreviewModal = new bootstrap.Modal(document.getElementById('emailPreviewModal'));
                        emailPreviewModal.show();
                    })
                    .catch(error => {
                        console.error('Error fetching hotel data:', error);
                        let errorMessage = 'Failed to generate email preview.';

                        // Add more detailed error information
                        if (error.message) {
                            errorMessage += ' Error: ' + error.message;
                        }

                        // Check if error is a Response object (Fetch API)
                        if (error instanceof Response) {
                            errorMessage += ' Status: ' + error.status;
                            if (error.statusText) {
                                errorMessage += ' (' + error.statusText + ')';
                            }
                        } 
                        // Check for Axios-style response property
                        else if (error.response) {
                            errorMessage += ' Status: ' + error.response.status;
                            if (error.response.statusText) {
                                errorMessage += ' (' + error.response.statusText + ')';
                            }
                        }
                        // Check if error has status directly
                        else if (error.status) {
                            errorMessage += ' Status: ' + error.status;
                            if (error.statusText) {
                                errorMessage += ' (' + error.statusText + ')';
                            }
                        }

                        Swal.fire({
                            title: 'Error',
                            html: `
                                <p>${errorMessage}</p>
                                <div class="alert alert-info mt-3">
                                    <strong>Troubleshooting tips:</strong>
                                    <ul class="mb-0 mt-2 text-start">
                                        <li>Check if the hotel has valid contact information</li>
                                        <li>Verify that the email template contains valid variables</li>
                                        <li>Make sure you have selected a valid email template</li>
                                        <li>Try refreshing the page and trying again</li>
                                    </ul>
                                </div>
                            `,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    });
            });
        });
    });
</script>
@endsection
