@extends('layouts/layoutSavAdminMaster')

@section('title', 'Hotel Chain Details')

<!-- Vendor Styles -->
@section('vendor-style')
@vite([
'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss'
])
@endsection

<!-- Vendor Scripts -->
@section('vendor-script')
@vite([
'resources/assets/vendor/libs/jquery/jquery.js',
'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js'
])
@endsection

<!-- Page Scripts -->
@section('page-script')
<script>
    $(document).ready(function() {
        $('.datatables-markets').DataTable({
            paging: true,
            lengthChange: false,
            searching: false,
            ordering: true,
            info: true,
            autoWidth: false,
            responsive: true
        });
        
        $('.datatables-brands').DataTable({
            paging: true,
            lengthChange: false,
            searching: false,
            ordering: true,
            info: true,
            autoWidth: false,
            responsive: true
        });
        
        $('.datatables-hotels').DataTable({
            paging: true,
            lengthChange: false,
            searching: false,
            ordering: true,
            info: true,
            autoWidth: false,
            responsive: true
        });
    });
</script>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Hotel Chain Details</h5>
                <div>
                    <a href="{{ route('hotel-chains.edit', $hotelChain->id) }}" class="btn btn-primary me-2">
                        <i class="ti ti-edit me-1"></i> Edit
                    </a>
                    <a href="{{ route('hotel-chains.index') }}" class="btn btn-secondary">
                        <i class="ti ti-arrow-left me-1"></i> Back to List
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <h6 class="fw-semibold">Chain Name</h6>
                            <p>{{ $hotelChain->name }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <h6 class="fw-semibold">Created At</h6>
                            <p>{{ $hotelChain->created_at->format('F d, Y H:i:s') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Markets -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Markets ({{ $hotelChain->markets->count() }})</h5>
                <a href="{{ route('hotel-markets.create') }}" class="btn btn-primary btn-sm">
                    <i class="ti ti-plus me-1"></i> Add Market
                </a>
            </div>
            <div class="card-body">
                @if($hotelChain->markets->count() > 0)
                    <div class="table-responsive">
                        <table class="datatables-markets table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Brands</th>
                                    <th>Hotels</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($hotelChain->markets as $market)
                                    <tr>
                                        <td>{{ $market->id }}</td>
                                        <td>{{ $market->name }}</td>
                                        <td>{{ $market->brands->count() }}</td>
                                        <td>{{ $market->hotels->count() }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <a href="{{ route('hotel-markets.show', $market->id) }}" class="btn btn-sm btn-icon">
                                                    <i class="ti ti-eye text-primary"></i>
                                                </a>
                                                <a href="{{ route('hotel-markets.edit', $market->id) }}" class="btn btn-sm btn-icon">
                                                    <i class="ti ti-edit text-primary"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-center">No markets found for this chain.</p>
                @endif
            </div>
        </div>
        
        <!-- Brands -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Brands ({{ $hotelChain->brands->count() }})</h5>
                <a href="{{ route('hotel-brands.create') }}" class="btn btn-primary btn-sm">
                    <i class="ti ti-plus me-1"></i> Add Brand
                </a>
            </div>
            <div class="card-body">
                @if($hotelChain->brands->count() > 0)
                    <div class="table-responsive">
                        <table class="datatables-brands table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Icon</th>
                                    <th>Name</th>
                                    <th>Market</th>
                                    <th>Hotels</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($hotelChain->brands as $brand)
                                    <tr>
                                        <td>{{ $brand->id }}</td>
                                        <td>
                                            @if($brand->icon)
                                                <img src="{{ asset('storage/' . $brand->icon) }}" alt="{{ $brand->name }}" class="rounded" width="40" height="40">
                                            @else
                                                <div class="avatar avatar-sm bg-light-primary rounded">
                                                    <span class="avatar-initial rounded">{{ $brand->name[0] }}</span>
                                                </div>
                                            @endif
                                        </td>
                                        <td>{{ $brand->name }}</td>
                                        <td>{{ $brand->market->name }}</td>
                                        <td>{{ $brand->hotels->count() }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <a href="{{ route('hotel-brands.show', $brand->id) }}" class="btn btn-sm btn-icon">
                                                    <i class="ti ti-eye text-primary"></i>
                                                </a>
                                                <a href="{{ route('hotel-brands.edit', $brand->id) }}" class="btn btn-sm btn-icon">
                                                    <i class="ti ti-edit text-primary"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-center">No brands found for this chain.</p>
                @endif
            </div>
        </div>
        
        <!-- Hotels -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Hotels ({{ $hotelChain->hotels->count() }})</h5>
            </div>
            <div class="card-body">
                @if($hotelChain->hotels->count() > 0)
                    <div class="table-responsive">
                        <table class="datatables-hotels table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Brand</th>
                                    <th>Market</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($hotelChain->hotels as $hotel)
                                    <tr>
                                        <td>{{ $hotel->id }}</td>
                                        <td>{{ $hotel->name }}</td>
                                        <td>{{ $hotel->brand->name ?? 'N/A' }}</td>
                                        <td>{{ $hotel->market->name ?? 'N/A' }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <a href="{{ route('hotel.show', $hotel->id) }}" class="btn btn-sm btn-icon">
                                                    <i class="ti ti-eye text-primary"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-center">No hotels found for this chain.</p>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection