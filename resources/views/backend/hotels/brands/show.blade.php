@extends('layouts/layoutSavAdminMaster')

@section('title', 'Hotel Brand Details')

<!-- Vendor Styles -->
@section('vendor-style')
@vite([
'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss'
])
@endsection

<!-- Vendor Scripts -->
@section('vendor-script')
@vite([
'resources/assets/vendor/libs/jquery/jquery.js',
'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js'
])
@endsection

<!-- Page Scripts -->
@section('page-script')
<script>
    $(document).ready(function() {
        $('.datatables-hotels').DataTable({
            paging: true,
            lengthChange: false,
            searching: false,
            ordering: true,
            info: true,
            autoWidth: false,
            responsive: true
        });
    });
</script>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Hotel Brand Details</h5>
                <div>
                    <a href="{{ route('hotel-brands.edit', $hotelBrand->id) }}" class="btn btn-primary me-2">
                        <i class="ti ti-edit me-1"></i> Edit
                    </a>
                    <a href="{{ route('hotel-brands.index') }}" class="btn btn-secondary">
                        <i class="ti ti-arrow-left me-1"></i> Back to List
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <h6 class="fw-semibold">Brand Name</h6>
                                <p>{{ $hotelBrand->name }}</p>
                            </div>

                            <div class="col-md-12 mb-3">
                                <h6 class="fw-semibold">Hotel Chain</h6>
                                <p>
                                    <a href="{{ route('hotel-chains.show', $hotelBrand->chain->id) }}">
                                        {{ $hotelBrand->chain->name }}
                                    </a>
                                </p>
                            </div>

                            <div class="col-md-12 mb-3">
                                <h6 class="fw-semibold">Hotel Market</h6>
                                <p>
                                    <a href="{{ route('hotel-markets.show', $hotelBrand->market->id) }}">
                                        {{ $hotelBrand->market->name }}
                                    </a>
                                </p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <h6 class="fw-semibold">Created At</h6>
                                <p>{{ $hotelBrand->created_at->format('F d, Y H:i:s') }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <h6 class="fw-semibold">Updated At</h6>
                                <p>{{ $hotelBrand->updated_at->format('F d, Y H:i:s') }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 d-flex justify-content-center align-items-center">
                        @if($hotelBrand->icon)
                            <div class="text-center">
                                <img src="{{ asset('storage/' . $hotelBrand->icon) }}" alt="{{ $hotelBrand->name }}" class="rounded img-fluid" style="max-height: 200px;">
                                <p class="mt-2 text-muted">Brand Logo</p>
                            </div>
                        @else
                            <div class="text-center">
                                <div class="avatar avatar-xl bg-light-primary rounded">
                                    <span class="avatar-initial rounded">{{ $hotelBrand->name[0] }}</span>
                                </div>
                                <p class="mt-2 text-muted">No logo available</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Hotels -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Hotels ({{ $hotelBrand->hotels->count() }})</h5>
            </div>
            <div class="card-body">
                @if($hotelBrand->hotels->count() > 0)
                    <div class="table-responsive">
                        <table class="datatables-hotels table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Market</th>
                                    <th>Chain</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($hotelBrand->hotels as $hotel)
                                    <tr>
                                        <td>{{ $hotel->id }}</td>
                                        <td>{{ $hotel->name }}</td>
                                        <td>{{ $hotel->market->name ?? 'N/A' }}</td>
                                        <td>{{ $hotel->chain->name ?? 'N/A' }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <a href="{{ route('hotel.show', $hotel->id) }}" class="btn btn-sm btn-icon">
                                                    <i class="ti ti-eye text-primary"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-center">No hotels found for this brand.</p>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
