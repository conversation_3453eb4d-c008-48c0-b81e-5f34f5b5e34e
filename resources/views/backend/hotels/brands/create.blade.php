@extends('layouts/layoutSavAdminMaster')

@section('title', 'Create Hotel Brand')

<!-- Vendor Styles -->
@section('vendor-style')
@vite([
'resources/assets/vendor/libs/select2/select2.scss',
'resources/assets/vendor/libs/@form-validation/form-validation.scss',
'resources/assets/vendor/libs/dropzone/dropzone.scss'
])
@endsection

<!-- Vendor Scripts -->
@section('vendor-script')
@vite([
'resources/assets/vendor/libs/jquery/jquery.js',
'resources/assets/vendor/libs/select2/select2.js',
'resources/assets/vendor/libs/@form-validation/popular.js',
'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
'resources/assets/vendor/libs/@form-validation/auto-focus.js',
'resources/assets/vendor/libs/dropzone/dropzone.js'
])
@endsection

<!-- Page Scripts -->
@section('page-script')
<script>
    window.addEventListener('load', function() {
        // Initialize select2
        $('.select2').select2();

        // Handle chain change to update markets dropdown
        $('#hotel_chain_id').on('change', function() {
            const chainId = $(this).val();
            if (chainId) {
                // Clear and disable markets dropdown while loading
                $('#hotel_market_id').empty().prop('disabled', true);
                $('#hotel_market_id').append('<option value="">Loading markets...</option>');

                // Fetch markets for the selected chain
                console.log('Fetching markets for chain ID:', chainId);
                console.log('AJAX URL:', "{{ route('hotel-brands.markets-by-chain') }}");

                $.ajax({
                    url: "{{ route('hotel-brands.markets-by-chain') }}",
                    type: "GET",
                    data: { chain_id: chainId },
                    success: function(response) {
                        console.log('AJAX Success Response:', response);
                        // Clear the dropdown first
                        $('#hotel_market_id').empty();

                        if (response.length > 0) {
                            // Enable the dropdown and populate it with markets
                            $('#hotel_market_id').prop('disabled', false);
                            $('#hotel_market_id').append('<option value="">Select Market</option>');
                            response.forEach(function(market) {
                                $('#hotel_market_id').append('<option value="' + market.id + '">' + market.name + '</option>');
                            });
                        } else {
                            // Disable the dropdown and add a disabled option when no markets are found
                            $('#hotel_market_id').prop('disabled', true);
                            $('#hotel_market_id').append('<option value="" disabled>No markets found for this chain</option>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', status, error);
                        console.error('Response:', xhr.responseText);
                        // In case of error, clear the dropdown and show an error message
                        $('#hotel_market_id').empty();
                        $('#hotel_market_id').prop('disabled', true);
                        $('#hotel_market_id').append('<option value="" disabled>Error loading markets</option>');
                    }
                });
            } else {
                // Clear markets dropdown if no chain selected
                $('#hotel_market_id').empty().prop('disabled', true);
                $('#hotel_market_id').append('<option value="" disabled>Select Chain First</option>');
            }
        });

        // Initialize form validation
        const formValidation = FormValidation.formValidation(document.getElementById('brandForm'), {
            fields: {
                name: {
                    validators: {
                        notEmpty: {
                            message: 'Please enter brand name'
                        },
                        stringLength: {
                            min: 2,
                            max: 255,
                            message: 'Brand name must be between 2 and 255 characters'
                        }
                    }
                },
                hotel_chain_id: {
                    validators: {
                        notEmpty: {
                            message: 'Please select a hotel chain'
                        }
                    }
                },
                hotel_market_id: {
                    validators: {
                        notEmpty: {
                            message: 'Please select a hotel market'
                        }
                    }
                },
                icon: {
                    validators: {
                        file: {
                            extension: 'jpg,jpeg,png,gif,svg',
                            type: 'image/jpeg,image/png,image/gif,image/svg+xml',
                            maxSize: 2097152, // 2MB
                            message: 'The selected file is not valid. Only JPG, PNG, GIF, and SVG files up to 2MB are allowed.'
                        }
                    }
                }
            },
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap5: new FormValidation.plugins.Bootstrap5({
                    eleValidClass: '',
                    rowSelector: '.mb-3'
                }),
                submitButton: new FormValidation.plugins.SubmitButton(),
                autoFocus: new FormValidation.plugins.AutoFocus()
            }
        }).on('core.form.valid', function() {
            // Submit the form when it's valid
            document.getElementById('brandForm').submit();
        });

        // Preview uploaded image
        $('#icon').on('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    $('#icon-preview').attr('src', e.target.result).removeClass('d-none');
                    $('.icon-placeholder').addClass('d-none');
                }
                reader.readAsDataURL(file);
            } else {
                $('#icon-preview').addClass('d-none');
                $('.icon-placeholder').removeClass('d-none');
            }
        });

        // Trigger chain change to load markets
        $('#hotel_chain_id').trigger('change');

        // Handle old values if form was submitted with errors
        @if(old('hotel_chain_id'))
            // Set the previously selected market after markets are loaded
            const oldMarketId = '{{ old('hotel_market_id') }}';
            if (oldMarketId) {
                setTimeout(function() {
                    $('#hotel_market_id').val(oldMarketId).trigger('change');
                }, 1000); // Give some time for the AJAX request to complete
            }
        @endif
    });
</script>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Create Hotel Brand</h5>
                <a href="{{ route('hotel-brands.index') }}" class="btn btn-secondary">
                    <i class="ti ti-arrow-left me-1"></i> Back to List
                </a>
            </div>
            <div class="card-body">
                <form id="brandForm" action="{{ route('hotel-brands.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="hotel_chain_id" class="form-label">Hotel Chain</label>
                                <select class="form-select select2 @error('hotel_chain_id') is-invalid @enderror" id="hotel_chain_id" name="hotel_chain_id" required>
                                    <option value="">Select Hotel Chain</option>
                                    @foreach($chains as $chain)
                                        <option value="{{ $chain->id }}" {{ old('hotel_chain_id') == $chain->id ? 'selected' : '' }}>
                                            {{ $chain->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('hotel_chain_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="hotel_market_id" class="form-label">Hotel Market</label>
                                <select class="form-select select2 @error('hotel_market_id') is-invalid @enderror" id="hotel_market_id" name="hotel_market_id" required>
                                    <option value="">Select Chain First</option>
                                </select>
                                @error('hotel_market_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="name" class="form-label">Brand Name</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" placeholder="Enter brand name" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="icon" class="form-label">Brand Logo</label>
                                <div class="d-flex flex-column align-items-center mb-3">
                                    <div class="icon-placeholder avatar avatar-xl bg-light-primary rounded mb-2">
                                        <span class="avatar-initial rounded">
                                            <i class="ti ti-upload fs-3"></i>
                                        </span>
                                    </div>
                                    <img id="icon-preview" src="#" alt="Logo Preview" class="rounded mb-2 d-none" style="max-width: 100%; max-height: 150px;">
                                </div>
                                <input type="file" class="form-control @error('icon') is-invalid @enderror" id="icon" name="icon" accept="image/*">
                                <small class="text-muted">Accepted formats: JPG, PNG, GIF, SVG. Max size: 2MB.</small>
                                @error('icon')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary me-2">Create Brand</button>
                        <a href="{{ route('hotel-brands.index') }}" class="btn btn-label-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@endsection
