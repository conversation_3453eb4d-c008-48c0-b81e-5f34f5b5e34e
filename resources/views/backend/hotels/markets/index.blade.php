@extends('layouts/layoutSavAdminMaster')

@section('title', 'Hotel Markets Management')

<!-- Vendor Styles -->
@section('vendor-style')
@vite([
'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
'resources/assets/vendor/libs/select2/select2.scss',
'resources/assets/vendor/libs/@form-validation/form-validation.scss',
'resources/assets/vendor/libs/animate-css/animate.scss',
'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

<!-- Vendor Scripts -->
@section('vendor-script')
@vite([
'resources/assets/vendor/libs/jquery/jquery.js',
'resources/assets/vendor/libs/moment/moment.js',
'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
'resources/assets/vendor/libs/select2/select2.js',
'resources/assets/vendor/libs/@form-validation/popular.js',
'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
'resources/assets/vendor/libs/@form-validation/auto-focus.js',
'resources/assets/vendor/libs/cleavejs/cleave.js',
'resources/assets/vendor/libs/cleavejs/cleave-phone.js',
'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

<!-- Page Scripts -->
@section('page-script')
<script>
    $(document).ready(function() {
        $('.datatables-markets').DataTable({
            processing: true,
            serverSide: false,
            // No AJAX, using server-side rendered data
            columns: [
                { data: 'id', name: 'id' },
                { data: 'name', name: 'name' },
                { data: 'chain.name', name: 'chain.name' },
                { data: 'brands_count', name: 'brands_count', searchable: false },
                { data: 'hotels_count', name: 'hotels_count', searchable: false },
                { data: 'actions', name: 'actions', orderable: false, searchable: false }
            ]
        });

        // Handle delete confirmation
        $(document).on('click', '.delete-record', function(e) {
            e.preventDefault();
            const form = $(this).closest('form');

            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, delete it!',
                customClass: {
                    confirmButton: 'btn btn-primary me-3',
                    cancelButton: 'btn btn-label-secondary'
                },
                buttonsStyling: false
            }).then(function (result) {
                if (result.value) {
                    form.submit();
                }
            });
        });
    });
</script>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Hotel Markets</h5>
                <a href="{{ route('hotel-markets.create') }}" class="btn btn-primary">
                    <i class="ti ti-plus me-1"></i> Add New Market
                </a>
            </div>
            <div class="card-datatable table-responsive">
                <table class="datatables-markets table">
                    <thead class="border-top">
                        <tr>
                            <th>ID</th>
                            <th>Market Name</th>
                            <th>Chain</th>
                            <th class="text-center">Brands</th>
                            <th class="text-center">Hotels</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($markets as $market)
                        <tr>
                            <td>{{ $market->id }}</td>
                            <td>{{ $market->name }}</td>
                            <td>{{ $market->chain->name }}</td>
                            <td class="text-center">{{ $market->brands_count }}</td>
                            <td class="text-center">{{ $market->hotels_count }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <a href="{{ route('hotel-markets.show', $market->id) }}" class="btn btn-sm btn-icon">
                                        <i class="ti ti-eye text-primary"></i>
                                    </a>
                                    <a href="{{ route('hotel-markets.edit', $market->id) }}" class="btn btn-sm btn-icon">
                                        <i class="ti ti-edit text-primary"></i>
                                    </a>
                                    <form action="{{ route('hotel-markets.destroy', $market->id) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-icon delete-record">
                                            <i class="ti ti-trash text-danger"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@if(session('success'))
<div class="alert alert-success alert-dismissible fade show" role="alert">
    {{ session('success') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    {{ session('error') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif
@endsection
