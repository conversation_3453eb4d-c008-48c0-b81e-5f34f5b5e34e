@extends('layouts/layoutSavAdminMaster')

@section('title', 'Hotel Map with Filters')

<!-- Vendor Styles -->
@section('vendor-style')
@vite([
'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
'resources/assets/vendor/libs/select2/select2.scss',
'resources/assets/vendor/libs/@form-validation/form-validation.scss',
'resources/assets/vendor/libs/animate-css/animate.scss',
'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
'resources/assets/vendor/libs/nouislider/nouislider.scss'
])
@endsection

<!-- Vendor Scripts -->
@section('vendor-script')
@vite([
'resources/assets/vendor/libs/jquery/jquery.js',
'resources/assets/vendor/libs/moment/moment.js',
'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
'resources/assets/vendor/libs/select2/select2.js',
'resources/assets/vendor/libs/@form-validation/popular.js',
'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
'resources/assets/vendor/libs/@form-validation/auto-focus.js',
'resources/assets/vendor/libs/cleavejs/cleave.js',
'resources/assets/vendor/libs/cleavejs/cleave-phone.js',
'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
'resources/assets/vendor/libs/nouislider/nouislider.js'
])
@endsection

<!-- Page Scripts -->
@section('page-script')
<script>
    // Define API routes for AJAX requests
    window.routes = {
        hotelApiSearchCitiesUrl: @json(route('api.hotel-api.search-cities'))
    };

    // Pass hotels data from controller to JavaScript
    var initialHotels = @json($hotels ?? []);

    // Pass partner type labels from PHP to JavaScript
    window.partnerTypeLabels = @json($partnerTypes);
</script>
@vite(['resources/assets/js/hotel-map-filters.js'])
<script>
    // Handle filter collapse/expand functionality
    document.addEventListener('DOMContentLoaded', function() {
        const toggleBtn = document.getElementById('toggleFiltersBtn');
        const filterBody = document.getElementById('filterBody');
        const filterResults = document.getElementById('filterResults');

        // Function to update icon based on collapse state
        function updateToggleIcon(isCollapsed) {
            const icon = toggleBtn.querySelector('i');
            if (isCollapsed) {
                icon.classList.remove('ti-chevron-up');
                icon.classList.add('ti-chevron-down');
                toggleBtn.setAttribute('title', 'Expand filters');
                if (filterResults) {
                    filterResults.classList.remove('d-none');
                }
            } else {
                icon.classList.remove('ti-chevron-down');
                icon.classList.add('ti-chevron-up');
                toggleBtn.setAttribute('title', 'Collapse filters');
                if (filterResults) {
                    filterResults.classList.add('d-none');
                }
            }
        }

        // Function to adjust hotel list height based on filter collapse state
        function adjustHotelListHeight() {
            const hotelListContainer = document.querySelector('.hotel-list-container');
            const mapContainer = document.getElementById('hotelMap');

            if (!hotelListContainer || !mapContainer) return;

            if (!filterBody.classList.contains('show')) {
                // Filters are collapsed, make hotel list fill available space
                const mapHeight = mapContainer.offsetHeight;
                const hotelListCard = hotelListContainer.closest('.card');
                const hotelListHeader = hotelListCard.querySelector('.card-header');
                const headerHeight = hotelListHeader ? hotelListHeader.offsetHeight : 0;

                // Set the height to match the map height minus the header height
                hotelListContainer.style.height = (mapHeight - headerHeight) + 'px';
            } else {
                // Filters are expanded, reset to default height
                hotelListContainer.style.height = '500px';
            }
        }

        // Function to collapse filters
        function collapseFilters() {
            const bsCollapse = new bootstrap.Collapse(filterBody, {
                toggle: false
            });
            bsCollapse.hide();
        }

        // Listen for Bootstrap collapse events
        filterBody.addEventListener('hidden.bs.collapse', function() {
            updateToggleIcon(true);
            // Save collapsed state in localStorage
            localStorage.setItem('hotelFiltersCollapsed', 'true');
            // Adjust hotel list height when filters are collapsed
            adjustHotelListHeight();
        });

        filterBody.addEventListener('shown.bs.collapse', function() {
            updateToggleIcon(false);
            // Save expanded state in localStorage
            localStorage.setItem('hotelFiltersCollapsed', 'false');
            // Adjust hotel list height when filters are expanded
            adjustHotelListHeight();
        });

        // Check if the page was loaded with filter parameters
        const hasFilterParams = window.location.search.length > 1;

        // If we have filter parameters, collapse the filters section
        if (hasFilterParams) {
            // Use setTimeout to ensure the DOM is fully loaded
            setTimeout(function() {
                collapseFilters();
                // Adjust hotel list height after collapsing filters
                adjustHotelListHeight();
            }, 100);
        } else {
            // If no filter parameters, check if we have a saved state in localStorage
            const savedState = localStorage.getItem('hotelFiltersCollapsed');
            if (savedState === 'true') {
                // Collapse the filters if they were collapsed before
                collapseFilters();
                // Adjust hotel list height after collapsing filters
                adjustHotelListHeight();
            } else {
                // Otherwise, update icon based on initial state
                if (filterBody.classList.contains('show')) {
                    updateToggleIcon(false);
                } else {
                    updateToggleIcon(true);
                }
                // Adjust hotel list height based on initial state
                adjustHotelListHeight();
            }
        }

        // Adjust hotel list height when window is resized
        window.addEventListener('resize', function() {
            adjustHotelListHeight();
        });

        // Auto-collapse filters after applying them
        document.getElementById('showOnMapBtn').addEventListener('click', function() {
            // Reset showOnlySelected to false when the user clicks "Show on map"
            if (typeof window.showOnlySelected !== 'undefined') {
                window.showOnlySelected = false;
            } else if (typeof showOnlySelected !== 'undefined') {
                showOnlySelected = false;
            }

            // Reset the button appearance to its default state
            const toggleBtn = document.getElementById('toggleSelectedHotelsBtn');
            if (toggleBtn) {
                toggleBtn.classList.remove('btn-primary');
                toggleBtn.classList.add('btn-outline-primary');
                toggleBtn.setAttribute('title', 'Show on map only selected');
            }

            // Clear the localStorage value
            try {
                localStorage.removeItem('showOnlySelected');
                console.log('Reset showOnlySelected to false when "Show on map" was clicked');
            } catch (e) {
                console.error('Error resetting showOnlySelected:', e);
            }

            // Use setTimeout to allow the filter operation to complete first
            setTimeout(function() {
                // Only collapse if there are hotels found
                const hotelsCountText = document.getElementById('hotelsCount').textContent;
                const hotelsCount = parseInt(hotelsCountText);
                if (!isNaN(hotelsCount) && hotelsCount > 0) {
                    collapseFilters();
                    // Adjust hotel list height after collapsing filters
                    adjustHotelListHeight();
                }

                // Update marker visibility to show all markers
                if (typeof updateMarkersVisibility === 'function') {
                    updateMarkersVisibility();
                }
            }, 1000);
        });

        // Add a floating button to expand filters when they're collapsed
        // This will be visible only when scrolling down the page and filters are collapsed
        const floatingBtn = document.createElement('button');
        floatingBtn.className = 'btn btn-primary btn-icon rounded-circle position-fixed d-none';
        floatingBtn.id = 'floatingFilterBtn';
        floatingBtn.style.bottom = '20px';
        floatingBtn.style.left = '20px';
        floatingBtn.style.zIndex = '1050';
        floatingBtn.setAttribute('data-bs-toggle', 'collapse');
        floatingBtn.setAttribute('data-bs-target', '#filterBody');
        floatingBtn.setAttribute('aria-expanded', 'false');
        floatingBtn.setAttribute('aria-controls', 'filterBody');
        floatingBtn.setAttribute('title', 'Expand filters');
        floatingBtn.innerHTML = '<i class="ti ti-adjustments"></i>';
        document.body.appendChild(floatingBtn);

        // Show/hide floating button based on scroll position and filter collapse state
        window.addEventListener('scroll', function() {
            if (!filterBody.classList.contains('show') && window.scrollY > 200) {
                floatingBtn.classList.remove('d-none');
            } else {
                floatingBtn.classList.add('d-none');
            }
        });
    });
</script>
<script>
    // Define the initializeMapFunctionality function in the global scope
    function initializeMapFunctionality() {
        // Check if map is properly initialized
        if (typeof window.globalMap === 'undefined') {
            console.error('Map initialization failed: window.globalMap is undefined');
            return;
        }

        console.log('Map has been initialized successfully', window.globalMap);

        // Trigger any initialization that depends on the map
        // Use jQuery if available, otherwise use native DOM events
        if (typeof jQuery !== 'undefined') {
            jQuery(document).trigger('mapInitialized');
        } else if (typeof $ !== 'undefined') {
            $(document).trigger('mapInitialized');
        } else {
            // Fallback to native DOM events if jQuery is not available
            document.dispatchEvent(new CustomEvent('mapInitialized'));
            console.log('Fallback: CustomEvent mapInitialized dispatched from initializeMapFunctionality');
        }
    }

    // Define global variables for map and infoWindow
    window.globalMap = null;
    window.globalInfoWindow = null;

    // Initialize the map when the API is loaded
    async function initMap() {
        try {
            console.log('initMap function called');

            // Request needed libraries.
            const { Map } = await google.maps.importLibrary("maps");
            const { AdvancedMarkerElement } = await google.maps.importLibrary("marker");
            console.log('Maps and Marker libraries loaded');

            // Make AdvancedMarkerElement available globally
            window.AdvancedMarkerElement = AdvancedMarkerElement;

            // Check if the map container exists
            const mapElement = document.getElementById("hotelMap");
            if (!mapElement) {
                console.error('Map container element not found');
                return;
            }

            // Initialize the map with fullscreen control enabled
            window.globalMap = new Map(mapElement, {
                center: { lat: 0, lng: 0 },
                zoom: 2,
                mapTypeId: "roadmap",
                mapId: "{{ config('services.google_maps.map_id', '') }}",
                fullscreenControl: true,
                fullscreenControlOptions: {
                    position: google.maps.ControlPosition.TOP_RIGHT
                }
            });

            console.log('Map created with mapId:', "{{ config('services.google_maps.map_id', '') }}", window.globalMap);

            // Method 0: Try to find and monitor the fullscreen button directly
            setTimeout(function() {
                try {
                    // Find the fullscreen button in the map
                    const mapContainer = document.getElementById('hotelMap');
                    if (mapContainer) {
                        // Look for the fullscreen button (it might have different classes depending on the Maps version)
                        const fullscreenButton = mapContainer.querySelector('button[title*="full screen"], button[aria-label*="full screen"], button.gm-fullscreen-control');

                        if (fullscreenButton) {
                            console.log('Found fullscreen button, adding click listener');

                            // Add a click listener to the fullscreen button
                            fullscreenButton.addEventListener('click', function() {
                                // Toggle the fullscreen state using the centralized function
                                window.updateGoogleMapsFullscreenState(
                                    !window.isGoogleMapsFullscreen, 
                                    'Fullscreen button click'
                                );
                            });
                        } else {
                            console.log('Fullscreen button not found, will rely on other detection methods');
                        }
                    }
                } catch (error) {
                    console.error('Error setting up fullscreen button listener:', error);
                }
            }, 1000); // Wait for the map to fully initialize

            // Create a single info window to be used for all markers
            window.globalInfoWindow = new google.maps.InfoWindow();

            // Add a variable to track Google Maps fullscreen state
            window.isGoogleMapsFullscreen = false;

            // Function to update the fullscreen state consistently across all detection methods
            window.updateGoogleMapsFullscreenState = function(isFullscreen, source) {
                // Only update and log if the state has changed
                if (window.isGoogleMapsFullscreen !== isFullscreen) {
                    window.isGoogleMapsFullscreen = isFullscreen;
                    console.log(`Google Maps fullscreen state changed to ${isFullscreen} (detected by: ${source})`);

                    // Dispatch a custom event that other code can listen for
                    const event = new CustomEvent('googleMapsFullscreenChanged', { 
                        detail: { isFullscreen: isFullscreen, source: source } 
                    });
                    document.dispatchEvent(event);
                }
            };

            // Add event listener to close info window when exiting fullscreen mode
            document.addEventListener('googleMapsFullscreenChanged', function(event) {
                // Check if we're exiting fullscreen mode
                if (event.detail && event.detail.isFullscreen === false) {
                    // If the info window is open, close it
                    if (window.globalInfoWindow) {
                        window.globalInfoWindow.close();
                        console.log('Closed hotel details modal after exiting fullscreen mode');
                    }
                }
            });

            console.log('Setting up fullscreen change event listeners');

            try {
                // Method 1: Using Google Maps API event
                google.maps.event.addListener(window.globalMap, 'fullscreenchanged', function() {
                    // Toggle the fullscreen state using the centralized function
                    window.updateGoogleMapsFullscreenState(!window.isGoogleMapsFullscreen, 'Google Maps API event');
                });

                // Method 2: Using the fullscreenchange event on the map container
                const mapContainer = document.getElementById('hotelMap');
                mapContainer.addEventListener('fullscreenchange', function() {
                    const isFullscreen = !!document.fullscreenElement;
                    window.updateGoogleMapsFullscreenState(isFullscreen, 'Map container fullscreenchange event');
                });

                // Method 3: Using the fullscreenchange event on the document with browser-specific variants
                function handleFullscreenChange() {
                    const mapContainer = document.getElementById('hotelMap');
                    const fullscreenElement = 
                        document.fullscreenElement || 
                        document.webkitFullscreenElement || 
                        document.mozFullScreenElement || 
                        document.msFullscreenElement;

                    const isMapFullscreen = fullscreenElement === mapContainer;

                    if (isMapFullscreen) {
                        window.updateGoogleMapsFullscreenState(true, 'Document fullscreenchange event (entered)');
                    } else if (window.isGoogleMapsFullscreen) {
                        window.updateGoogleMapsFullscreenState(false, 'Document fullscreenchange event (exited)');
                    }
                }

                // Add all browser-specific fullscreen change events
                document.addEventListener('fullscreenchange', handleFullscreenChange);
                document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
                document.addEventListener('mozfullscreenchange', handleFullscreenChange);
                document.addEventListener('MSFullscreenChange', handleFullscreenChange);

                console.log('Fullscreen change event listeners set up successfully');

                // Method 4: Fallback polling mechanism to detect fullscreen changes
                // This will periodically check if the map is in fullscreen mode
                let previousFullscreenState = false;
                window.fullscreenCheckInterval = setInterval(function() {
                    try {
                        // Check if the map container is in fullscreen mode
                        const mapContainer = document.getElementById('hotelMap');
                        if (!mapContainer) return;

                        // Method 4a: Check standard fullscreen API
                        const fullscreenElement = 
                            document.fullscreenElement || 
                            document.webkitFullscreenElement || 
                            document.mozFullScreenElement || 
                            document.msFullscreenElement;

                        let isMapFullscreen = fullscreenElement === mapContainer;

                        // Method 4b: Check for Google Maps specific fullscreen classes or styles
                        if (!isMapFullscreen) {
                            // Check if the map container or any of its parents have fullscreen-related classes
                            const mapDiv = mapContainer.querySelector('div[style*="position: absolute"]');
                            if (mapDiv) {
                                const computedStyle = window.getComputedStyle(mapDiv);
                                // Check if the map takes up the full viewport, which would indicate fullscreen mode
                                const isFullViewport = 
                                    computedStyle.position === 'fixed' || 
                                    computedStyle.width === '100vw' || 
                                    computedStyle.height === '100vh' ||
                                    mapDiv.classList.contains('gm-fullscreen-control-active');

                                if (isFullViewport) {
                                    isMapFullscreen = true;
                                    console.log('Detected Google Maps fullscreen via style/class check');
                                }
                            }
                        }

                        // Method 4c: Check if the map element has a fullscreen-specific attribute
                        if (!isMapFullscreen && window.globalMap) {
                            try {
                                // Try to access internal properties of the map object
                                const mapObject = window.globalMap;
                                if (mapObject.isFullScreen && mapObject.isFullScreen()) {
                                    isMapFullscreen = true;
                                    console.log('Detected Google Maps fullscreen via map object property');
                                }
                            } catch (innerError) {
                                // Ignore errors when trying to access internal properties
                            }
                        }

                        // Only update if the state has changed
                        if (isMapFullscreen !== previousFullscreenState) {
                            previousFullscreenState = isMapFullscreen;
                            window.updateGoogleMapsFullscreenState(isMapFullscreen, 'Fullscreen polling');
                        }
                    } catch (e) {
                        console.error('Error in fullscreen polling:', e);
                    }
                }, 1000); // Check every second

                console.log('Fullscreen polling mechanism set up');

                // Clean up the interval when the page is unloaded to prevent memory leaks
                window.addEventListener('beforeunload', function() {
                    if (window.fullscreenCheckInterval) {
                        clearInterval(window.fullscreenCheckInterval);
                        console.log('Fullscreen polling interval cleared');
                    }
                });
            } catch (error) {
                console.error('Error setting up fullscreen change event listeners:', error);
            }

            console.log('InfoWindow created');

            // Dispatch a custom event to notify that the map is initialized
            // We need to use jQuery's event system to be compatible with hotel-map-filters.js
            // Use a longer timeout and check for jQuery multiple times
            let attempts = 0;
            const maxAttempts = 20; // Increased from 10 to 20
            const checkInterval = 300; // Increased from 200ms to 300ms

            function checkJQueryAndTrigger() {
                if (typeof jQuery !== 'undefined') {
                    // Use jQuery instead of $ to avoid any issues
                    jQuery(document).trigger('mapInitialized');
                    console.log('jQuery mapInitialized event triggered');
                } else if (attempts < maxAttempts) {
                    attempts++;
                    console.log(`jQuery not available yet, attempt ${attempts}/${maxAttempts}`);
                    setTimeout(checkJQueryAndTrigger, checkInterval);
                } else {
                    console.error('jQuery not available after multiple attempts');
                    // Fallback to using a custom event if jQuery is not available
                    document.dispatchEvent(new CustomEvent('mapInitialized'));
                    console.log('Fallback: CustomEvent mapInitialized dispatched');

                    // Add a global variable to indicate jQuery wasn't available
                    window.jQueryNotAvailable = true;

                    // Try to load jQuery directly as a last resort
                    if (!window.jQuery) {
                        console.log('Attempting to load jQuery directly');
                        const jQueryScript = document.createElement('script');
                        jQueryScript.src = 'https://code.jquery.com/jquery-3.7.1.min.js';
                        jQueryScript.onload = function() {
                            console.log('jQuery loaded directly, triggering event');
                            jQuery(document).trigger('mapInitialized');
                        };
                        document.head.appendChild(jQueryScript);
                    }
                }
            }

            // Start checking for jQuery
            setTimeout(checkJQueryAndTrigger, 100);
        } catch (error) {
            console.error('Error initializing map:', error);
        }
    }

    // Wait for DOM to be fully loaded and ensure jQuery is available before loading Google Maps API
    document.addEventListener('DOMContentLoaded', function() {
        // Function to check if jQuery is available
        function checkJQueryAndLoadMap() {
            if (typeof jQuery !== 'undefined') {
                console.log('jQuery is available, loading Google Maps API');
                // Load the Google Maps JavaScript API
                const script = document.createElement('script');
                script.src = `https://maps.googleapis.com/maps/api/js?key={{ config('services.google_maps.api_key', 'YOUR_API_KEY_HERE') }}&map_id={{ config('services.google_maps.map_id', '') }}&callback=initMap&v=weekly&loading=async`;
                script.async = true;
                script.defer = true;
                document.head.appendChild(script);
            } else {
                console.log('jQuery not available yet, waiting...');
                setTimeout(checkJQueryAndLoadMap, 100);
            }
        }

        // Start checking for jQuery
        checkJQueryAndLoadMap();
    });
</script>
<script>
    // jQuery-dependent code moved to a separate script block
    // This will only execute after jQuery is loaded
    function initializeMapWhenReady() {
        console.log('jQuery is ready');

        // Listen for map initialization event using jQuery's event system
        jQuery(document).on('mapInitialized', function() {
            console.log('Map initialized event received, initializing map functionality');
            if (window.globalMap) {
                // Don't call initializeMapFunctionality here to avoid circular reference
                console.log('Map is ready, event handlers are set up');
            }
        });

        // If map is already initialized when jQuery is ready, initialize functionality
        if (window.globalMap) {
            console.log('Map already initialized when jQuery became ready');
            // We only need to set up event handlers once
            console.log('Map is ready, event handlers are set up');
        }
    }

    // Check if jQuery is available
    if (typeof jQuery !== 'undefined') {
        jQuery(document).ready(initializeMapWhenReady);
    } else {
        // Fallback for when jQuery might be loaded later
        window.addEventListener('load', function() {
            if (typeof jQuery !== 'undefined') {
                initializeMapWhenReady();
            } else {
                console.error('jQuery not available even after window load');
                // Listen for the custom event as a fallback
                document.addEventListener('mapInitialized', function() {
                    console.log('Custom event mapInitialized received');
                    if (window.globalMap) {
                        // Don't call initializeMapFunctionality to avoid circular reference
                        console.log('Map is ready, event handlers are set up (fallback)');
                    }
                });
            }
        });
    }
</script>
@endsection

@section('content')
@if(session('success'))
<div class="alert alert-success alert-dismissible fade show" role="alert">
    {{ session('success') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    {{ session('error') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

@if(session('warning'))
<div class="alert alert-warning alert-dismissible fade show" role="alert">
    {{ session('warning') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

<div class="row">
    <!-- Left Column (Filters and Hotel List) -->
    <div class="col-md-4 col-12 order-md-0 order-1">
        <!-- Hotel Filters -->
        <div class="card mb-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Hotel Filters</h5>
                <button type="button" class="btn btn-sm btn-icon" id="toggleFiltersBtn" data-bs-toggle="collapse" data-bs-target="#filterBody" aria-expanded="true" aria-controls="filterBody">
                    <i class="ti ti-chevron-up"></i>
                </button>
            </div>
            <div class="card-body collapse show" id="filterBody">
                <form id="hotelFilterForm" action="{{ route('admin.hotels.map') }}" method="GET">
                    <!-- City Dropdown -->
                    <div class="mb-3">
                        <label for="city_id" class="form-label">City (Center of Map)</label>
                        <select id="city_id" name="city_id" class="select2 form-select" required>
                            @if(isset($selectedCity))
                                <option value="{{ $selectedCity->id }}"  data-lat="{{$selectedCity->latitude}}" data-lng="{{$selectedCity->longitude}}" selected>{{ $selectedCity->name }} ({{ $selectedCity->level2Division->name }})</option>
                            @else
                                <option value="">Type at least 3 characters to search cities</option>
                            @endif
                            <!-- Cities will be loaded via AJAX when user types at least 3 characters -->
                        </select>
                    </div>

                    <!-- Distance Range Slider -->
                    <div class="mb-3">
                        <label class="form-label">Distance from City (km)</label>
                        <div id="distance-slider" class="mt-4 mb-3"></div>
                        <div class="d-flex justify-content-between">
                            <div>
                                <input type="number" id="min_distance_km" name="min_distance_km" class="form-control form-control-sm" placeholder="Min" value="{{ request('min_distance_km', '') }}">
                            </div>
                            <div>
                                <input type="number" id="max_distance_km" name="max_distance_km" class="form-control form-control-sm" placeholder="Max" value="{{ request('max_distance_km', '') }}">
                            </div>
                        </div>
                    </div>

                    <!-- Price Range Inputs -->
                    <div class="mb-3">
                        <label class="form-label">Price Range</label>
                        <div class="d-flex justify-content-between">
                            <div class="me-1">
                                <input type="number" id="min_price" name="min_price" class="form-control" placeholder="Min Price" value="{{ request('min_price', '') }}">
                            </div>
                            <div class="ms-1">
                                <input type="number" id="max_price" name="max_price" class="form-control" placeholder="Max Price" value="{{ request('max_price', '') }}">
                            </div>
                        </div>
                    </div>

                    <!-- Stars Filter -->
                    <div class="mb-3">
                        <label class="form-label">Hotel Stars</label>
                        <div class="d-flex flex-wrap">
                            @for($i = 1; $i <= 5; $i++)
                                <div class="form-check me-3">
                                    <input class="form-check-input" type="checkbox" name="stars[]" value="{{ $i }}" id="star{{ $i }}" {{ in_array($i, request('stars', [])) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="star{{ $i }}">
                                        @for($j = 0; $j < $i; $j++) ★ @endfor
                                    </label>
                                </div>
                            @endfor
                        </div>
                    </div>

                    <!-- Partner Type Filter -->
                    <div class="mb-3">
                        <label class="form-label">Partner Type</label>
                        <div class="d-flex flex-wrap">
                            @foreach($partnerTypes as $value => $label)
                                <div class="form-check me-3 mb-1">
                                    <input class="form-check-input" type="checkbox" name="partner_type[]" value="{{ $value }}" id="partner_{{ $value }}" {{ in_array($value, request('partner_type', [])) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="partner_{{ $value }}">
                                        {{ $label }}
                                    </label>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Hotel Chain Dropdown -->
                    <div class="mb-3">
                        <label for="hotel_chain" class="form-label">Hotel Chain</label>
                        <select id="hotel_chain" name="hotel_chain[]" class="select2 form-select" multiple>
                            @foreach($hotelChains as $chain)
                                <option value="{{ $chain->id }}" {{ in_array($chain->id, request('hotel_chain', [])) ? 'selected' : '' }}>{{ $chain->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Hotel Brand Dropdown -->
                    <div class="mb-3">
                        <label for="hotel_brand" class="form-label">Hotel Brand</label>
                        <select id="hotel_brand" name="hotel_brand[]" class="select2 form-select" multiple>
                            @foreach($hotelBrandsByMarket as $marketName => $brands)
                                <optgroup label="{{ $marketName }}">
                                    @foreach($brands as $brand)
                                        <option value="{{ $brand->id }}" data-chain="{{ $brand->hotel_chain_id }}" {{ in_array($brand->id, request('hotel_brand', [])) ? 'selected' : '' }}>{{ $brand->name }}</option>
                                    @endforeach
                                </optgroup>
                            @endforeach
                        </select>
                    </div>

                    <!-- Hotel Name Search -->
                    <div class="mb-3">
                        <label for="hotel_name" class="form-label">Hotel Name</label>
                        <input type="text" id="hotel_name" name="hotel_name" class="form-control" placeholder="Search by hotel name" value="{{ request('hotel_name', '') }}">
                    </div>

                    <!-- Is Claimed Filter -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="is_claimed" value="1" id="is_claimed" {{ request('is_claimed') ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_claimed">
                                Is claimed
                            </label>
                        </div>
                    </div>

                    <!-- Emails Filter -->
                    <div class="mb-3">
                        <label for="emails_filter" class="form-label">Emails</label>
                        <select id="emails_filter" name="emails_filter" class="select2 form-select">
                            <option value="">Select email filter</option>
                            <option value="default_emails" {{ request('emails_filter') == 'default_emails' ? 'selected' : '' }}>Default emails</option>
                            <option value="any_emails" {{ request('emails_filter') == 'any_emails' ? 'selected' : '' }}>Any emails</option>
                            <option value="no_emails" {{ request('emails_filter') == 'no_emails' ? 'selected' : '' }}>No emails</option>
                        </select>
                    </div>

                    <!-- Breakfast Filter -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="breakfast" value="1" id="breakfast" {{ request('breakfast') ? 'checked' : '' }}>
                            <label class="form-check-label" for="breakfast">
                                Breakfast
                            </label>
                        </div>
                    </div>

                    <!-- Minimum Capacity Filter -->
                    <div class="mb-3">
                        <label for="minimum_capacity" class="form-label">Minimum Capacity</label>
                        <input type="number" id="minimum_capacity" name="minimum_capacity" class="form-control" placeholder="Minimum guest capacity" value="{{ request('minimum_capacity', '') }}">
                    </div>

                    <!-- Accept Group Filter -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="accept_groups" value="1" id="accept_groups" {{ request('accept_groups') ? 'checked' : '' }}>
                            <label class="form-check-label" for="accept_groups">
                                Accept Group
                            </label>
                        </div>
                    </div>

                    <!-- Show on Map Button -->
                    <div class="d-grid gap-2">
                        <button type="submit" id="showOnMapBtn" class="btn btn-primary">
                            <i class="ti ti-map me-1"></i> Show on Map
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Hotel List -->
        <div class="card mb-3">
            <div class="card-header">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 me-2">Hotel List</h5>
                </div>
                <div class="d-flex align-items-center mb-2">
                    <select id="hotelSortOptions" class="form-select form-select-sm me-2" style="width: auto; min-width: 180px;">
                        <option value="name_asc">Sort by name: asc</option>
                        <option value="name_desc">Sort by name: desc</option>
                        <option value="selected_first">Sort by selection: first</option>
                        <option value="radius_asc">Sort by radius: asc</option>
                        <option value="radius_desc">Sort by radius: desc</option>
                        <option value="price_desc">Sort by price: asc</option>
                    </select>
                    <button id="toggleSelectedHotelsBtn" class="btn btn-sm btn-outline-primary me-2" title="Show on map only selected">
                        <i class="ti ti-map-pin"></i>
                    </button>
                    <button id="goToActionBtn" class="btn btn-sm btn-outline-primary" title="Go to action">
                        <i class="ti ti-external-link"></i>
                    </button>
                </div>
                <div class="d-flex align-items-center">
                    <span id="hotelsCount" class="badge bg-primary me-2">{{ count($hotels ?? []) }} hotels found</span>

                    <span id="selectedHotelsCount" class="badge bg-danger me-2">0 hotels selected</span>
                    <button id="resetSelectionBtn" class="btn btn-sm btn-outline-danger d-none">
                        <i class="ti ti-trash me-1"></i>Reset
                    </button>

                </div>
            </div>
            <div class="card-body p-0">
                <div class="hotel-list-container" style="height: 500px; overflow-y: auto;">
                    <div id="hotelResultsList" class="hotel-cards">
                        @if(isset($hotels) && count($hotels) > 0)
                            <!-- Hotels will be populated by JavaScript using initialHotels -->
                        @elseif(isset($tooManyResults) && $tooManyResults)
                            <div class="text-center py-4">
                                <i class="ti ti-alert-triangle mb-2" style="font-size: 2rem; color: #ea5455;"></i>
                                <p class="mb-0">Too many results ({{ $resultsCount }})</p>
                                <p class="text-muted small">Please refine your search criteria</p>
                            </div>
                        @elseif(request()->has('city_id'))
                            <div class="text-center py-4">
                                <i class="ti ti-mood-sad mb-2" style="font-size: 2rem;"></i>
                                <p class="mb-0">No hotels match your filter criteria</p>
                                <p class="text-muted small">Try adjusting your filters</p>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="ti ti-filter-off mb-2" style="font-size: 2rem;"></i>
                                <p class="mb-0">Apply filters to see hotels</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Right Column (Map) -->
    <div class="col-md-8 col-12 order-md-1 order-0">
        <!-- Map Card -->
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Hotel Map</h5>
                <div class="d-flex">
                    <div class="d-lg-none">
                        <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#mapContainer" aria-expanded="true" aria-controls="mapContainer">
                            Toggle Map
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0 collapse show" id="mapContainer">
                <div id="hotelMap"></div>
            </div>
        </div>
    </div>
</div>

<!-- CSS for Hotel Cards and Map -->
<style>
    /* Left column width limit */
    .col-md-4.col-12.order-md-0.order-1 {
        max-width: 425px;
    }

    /* Map container styles for full height */
    .col-md-8.col-12.order-md-1.order-0 {
        display: flex;
        flex-direction: column;
        width: calc(100% - 425px);
    }

    /* Responsive behavior for smaller screens */
    @media (max-width: 767.98px) {
        .col-md-4.col-12.order-md-0.order-1,
        .col-md-8.col-12.order-md-1.order-0 {
            max-width: 100%;
            width: 100%;
        }
    }

    .col-md-8.col-12.order-md-1.order-0 .card.h-100 {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .col-md-8.col-12.order-md-1.order-0 .card-body {
        flex: 1;
        padding: 0;
    }

    #hotelMap {
        height: calc(100vh - 200px); /* Subtract header height, footer height, and some padding */
        min-height: 500px; /* Ensure minimum height on small screens */
        width: 100%;
    }

    /* Adjust map height on window resize */
    @media (max-height: 768px) {
        #hotelMap {
            height: calc(100vh - 170px);
        }
    }

    @media (max-height: 576px) {
        #hotelMap {
            height: calc(100vh - 150px);
        }
    }

    .hotel-cards {
        display: flex;
        flex-direction: column;
        gap: 10px;
        padding: 10px;
    }

    .hotel-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 15px;
        transition: all 0.3s ease;
        cursor: pointer;
        background-color: #fff;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .hotel-card:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .hotel-card.active {
        border-color: #7367f0;
        background-color: #f8f6ff;
    }

    .hotel-card .hotel-name {
        font-weight: 600;
        margin-bottom: 5px;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .hotel-card .hotel-address {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 8px;
        overflow-wrap: break-word;
        word-wrap: break-word;
    }

    .hotel-card .hotel-stars {
        color: #ffc107;
        margin-left: 5px;
        display: inline-block;
        overflow-wrap: break-word;
        word-wrap: break-word;
    }

    .hotel-card .hotel-price {
        font-weight: 600;
        color: #28c76f;
        overflow-wrap: break-word;
        word-wrap: break-word;
    }

    .hotel-card .hotel-distance {
        font-size: 0.85rem;
        color: #6c757d;
        overflow-wrap: break-word;
        word-wrap: break-word;
    }

    .hotel-card .hotel-select-btn {
        margin-top: 10px;
    }

    /* Add margin-top to the Details and Map Pin buttons to match the Select button */
    .hotel-card .btn-info.me-2,
    .hotel-card .btn-secondary.me-2 {
        margin-top: 10px;
    }

    /* Hotel image styles */
    .hotel-card .hotel-image {
        min-width: 80px;
        width: 80px;
        height: 80px;
    }

    .hotel-card .hotel-image-col {
        min-width: 80px;
        width: 80px;
        margin-right: 10px;
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
    }

    .hotel-card .hotel-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .hotel-card .hotel-image-placeholder {
        min-width: 80px;
        width: 80px;
        height: 80px;
        background-color: #f5f5f5;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Hotel popup image styles */
    .hotel-popup .hotel-image {
        min-width: 100px;
        width: 100px;
        height: 100px;
    }

    .hotel-popup .hotel-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .hotel-popup .hotel-image-placeholder {
        min-width: 100px;
        width: 100px;
        height: 100px;
        background-color: #f5f5f5;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Hotel reviews styles */
    .hotel-card .hotel-reviews {
        display: flex;
        align-items: center;
    }

    .hotel-card .hotel-rating {
        font-weight: 600;
        color: #6c757d;
    }

    .hotel-card .hotel-rating-stars {
        display: flex;
        align-items: center;
    }

    .hotel-card .stars-container {
        display: inline-flex;
    }

    .hotel-card .filled-stars {
        color: #ffc107; /* Yellow color for filled stars */
    }

    .hotel-card .half-star {
        position: relative;
        display: inline-block;
        background: linear-gradient(90deg, #ffc107 50%, #e9ecef 50%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .hotel-card .unfilled-stars {
        color: #e9ecef; /* Light gray for unfilled stars */
    }

    .hotel-card .hotel-reviews-count {
        font-size: 0.85rem;
        color: #6c757d;
    }

    /* Hotel info icons styles */
    .hotel-info-icons {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: flex-start;
        padding: 5px 0;
        width: 100%;
    }

    .hotel-icon-item {
        margin: 2px;
        margin-right: 5px;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .hotel-icon-item .text-large {
        font-size: large;
    }

    .hotel-icon-value {
        margin-left: 5px;
        font-size: 0.8rem;
        color: #6c757d;
    }

    .fa-stack {
        width: 1em;
        height: 1em;
        line-height: 1em;
    }

    /* Ensure badges wrap properly */
    .hotel-card .badge {
        overflow-wrap: break-word;
        word-wrap: break-word;
        white-space: normal;
    }

    /* Responsive adjustments */
    @media (max-width: 991.98px) {
        .hotel-list-container, #hotelMap {
            height: 550px; /* Increased to account for footer */
        }

        /* Add tab-like navigation for mobile/tablet */
        .mobile-tabs {
            display: flex;
            margin-bottom: 15px;
        }

        .mobile-tab {
            flex: 1;
            text-align: center;
            padding: 10px;
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            cursor: pointer;
        }

        .mobile-tab.active {
            background-color: #7367f0;
            color: white;
            border-color: #7367f0;
        }
    }

    @media (max-width: 767.98px) {
        .hotel-list-container, #hotelMap {
            height: 450px; /* Increased to account for footer */
        }

        /* On very small screens, make the layout more compact */
        .hotel-card {
            padding: 10px;
        }

        .card-header {
            padding: 0.75rem;
        }

        .card-body {
            padding: 0;
        }
    }

    /* Animation for highlighting a hotel card */
    @keyframes highlight-pulse {
        0% { box-shadow: 0 0 0 0 rgba(115, 103, 240, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(115, 103, 240, 0); }
        100% { box-shadow: 0 0 0 0 rgba(115, 103, 240, 0); }
    }

    .hotel-card.highlight {
        animation: highlight-pulse 1.5s ease-out;
    }

    /* Selected hotel card style */
    .hotel-card.selected-hotel {
        background-color: #fff5f5; /* Light red background */
        border-color: #ea5455; /* Red border */
        border-width: 2px;
    }
</style>

<!-- Mobile Tabs (visible only on small screens) -->
<div class="d-md-none mobile-tabs mb-3">
    <div class="mobile-tab active" data-target="hotel-list">
        <i class="ti ti-list me-1"></i> Hotel List
    </div>
    <div class="mobile-tab" data-target="hotel-map">
        <i class="ti ti-map me-1"></i> Map View
    </div>
</div>

<script>
    // Function to adjust map height based on window size
    function adjustMapHeight() {
        const mapElement = document.getElementById('hotelMap');
        if (mapElement) {
            // Let CSS handle the height, but trigger a resize event for Google Maps
            if (window.globalMap) {
                google.maps.event.trigger(window.globalMap, 'resize');
            }
        }
    }

    // Add resize event listener
    window.addEventListener('resize', adjustMapHeight);

    // Call once on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Adjust map height when DOM is loaded
        adjustMapHeight();

        // Also adjust when map is initialized
        document.addEventListener('mapInitialized', adjustMapHeight);

        // Only run mobile tab functionality on mobile
        if (window.innerWidth < 768) {
            const tabs = document.querySelectorAll('.mobile-tab');
            const hotelListCol = document.querySelector('.col-md-4');
            const mapCol = document.querySelector('.col-md-8');

            // Initially hide the map on mobile
            mapCol.style.display = 'none';

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Update active tab
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');

                    // Show/hide content based on selected tab
                    const target = this.getAttribute('data-target');
                    if (target === 'hotel-list') {
                        hotelListCol.style.display = 'block';
                        mapCol.style.display = 'none';
                    } else {
                        hotelListCol.style.display = 'none';
                        mapCol.style.display = 'block';

                        // Trigger resize event to fix Google Maps display
                        if (window.globalMap) {
                            setTimeout(() => {
                                google.maps.event.trigger(window.globalMap, 'resize');
                            }, 100);
                        }
                    }
                });
            });
        }
    });
</script>

@endsection
