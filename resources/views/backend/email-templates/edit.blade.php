@extends('layouts/layoutSavAdminMaster')

@section('title', 'Edit Email Template')

<!-- Vendor Styles -->
@section('vendor-style')
@vite([
'resources/assets/vendor/libs/select2/select2.scss',
'resources/assets/vendor/libs/@form-validation/form-validation.scss',
'resources/assets/vendor/libs/quill/typography.scss',
'resources/assets/vendor/libs/quill/katex.scss',
'resources/assets/vendor/libs/quill/editor.scss'
])
@endsection

<!-- Vendor Scripts -->
@section('vendor-script')
@vite([
'resources/assets/vendor/libs/jquery/jquery.js',
'resources/assets/vendor/libs/select2/select2.js',
'resources/assets/vendor/libs/@form-validation/popular.js',
'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
'resources/assets/vendor/libs/@form-validation/auto-focus.js',
'resources/assets/vendor/libs/quill/katex.js',
'resources/assets/vendor/libs/quill/quill.js'
])
@endsection

<!-- Page Scripts -->
@section('page-script')
<script>
    // Ensure jQuery is available before executing code
    document.addEventListener('DOMContentLoaded', function() {
        // Check if jQuery is loaded
        if (typeof jQuery === 'undefined') {
            console.error('jQuery is not loaded. Showing fallback textarea without jQuery.');
            document.getElementById('fallback-content').style.display = 'block';
            if (document.getElementById('editor-container')) {
                document.getElementById('editor-container').style.display = 'none';
            }
            return;
        }

        // jQuery is available, proceed with initialization
        $(document).ready(function() {
            // Check if Quill is available
        if (typeof Quill === 'undefined') {
            console.error('Quill is not loaded. Showing fallback textarea.');
            $('#fallback-content').show();
            $('#editor-container').hide();

            // Set the fallback textarea as the active content field
            $('#fallback-content').attr('name', 'content');
            $('#content').attr('name', '_content_hidden');

            // Set a flag to indicate Quill is not available
            window.quillNotAvailable = true;

            // Initialize HTML source textarea with the fallback content
            $('#html-source').val($('#fallback-content').val());
        } else {
            try {
                // Initialize Quill editor and make it globally accessible
                window.quill = new Quill('#editor-container', {
                    modules: {
                        toolbar: [
                            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                            ['bold', 'italic', 'underline', 'strike'],
                            [{ 'color': [] }, { 'background': [] }],
                            [{ 'list': 'ordered' }, { 'list': 'bullet' }],
                            [{ 'align': [] }],
                            ['link'],
                            ['clean']
                        ]
                    },
                    placeholder: 'Compose your email template...',
                    theme: 'snow'
                });

                // Set initial content from old('content') if available, otherwise use $emailTemplate->content
                // This ensures the editor content is preserved when validation fails
                window.quill.root.innerHTML = {!! json_encode(old('content', $emailTemplate->content)) !!};

                // Initialize HTML source textarea with the same content
                $('#html-source').val(window.quill.root.innerHTML);

                console.log('Quill editor initialized successfully');

                // Hide the fallback textarea
                $('#fallback-content').hide();

                // Handle editor mode toggle
                $('input[name="editorMode"]').on('change', function() {
                    const mode = $(this).val();

                    if (window.quillNotAvailable) {
                        // When Quill is not available, toggle between fallback textarea and HTML source
                        if (mode === 'visual') {
                            // Switch to visual mode (fallback textarea)
                            $('#fallback-content').show();
                            $('#html-source').hide();

                            // Update fallback content from HTML source
                            $('#fallback-content').val($('#html-source').val());
                        } else {
                            // Switch to HTML source mode
                            $('#fallback-content').hide();
                            $('#html-source').show();

                            // Update HTML source from fallback content
                            $('#html-source').val($('#fallback-content').val());
                        }
                    } else {
                        // Normal toggle between Quill and HTML source when Quill is available
                        if (mode === 'visual') {
                            // Switch to visual mode
                            $('.ql-toolbar').show();
                            $('#editor-container').show();
                            $('#html-source').hide();

                            // Update Quill content from HTML source
                            if (window.quill) {
                                // Use dangerouslyPasteHTML to preserve exact HTML format
                                const htmlContent = $('#html-source').val();
                                window.quill.setText(''); // Clear existing content
                                window.quill.clipboard.dangerouslyPasteHTML(0, htmlContent);
                            }
                        } else {
                            // Switch to HTML source mode
                            $('.ql-toolbar').hide();
                            $('#editor-container').hide();
                            $('#html-source').show();

                            // Update HTML source from Quill content
                            if (window.quill) {
                                $('#html-source').val(window.quill.root.innerHTML);
                            }
                        }
                    }
                });

                // Function to extract variables from template content
                function extractVariablesFromContent(content) {
                    const regex = /\{\{([^}]+)\}\}/g;
                    const variables = new Set();
                    let match;

                    while ((match = regex.exec(content)) !== null) {
                        // Eliminăm spațiile de la începutul și sfârșitul numelui variabilei
                        variables.add(match[1].trim());
                    }

                    return Array.from(variables);
                }

                // Update hidden input with content from the active editor before form submission
                $('#templateForm').on('submit', function() {
                    const mode = $('input[name="editorMode"]:checked').val();
                    let content = '';

                    if (window.quillNotAvailable) {
                        // When Quill is not available, use either fallback textarea or HTML source
                        if (mode === 'visual') {
                            // Use fallback textarea content
                            content = $('#fallback-content').val();
                            $('#content').val(content);
                        } else {
                            // Use HTML source content
                            content = $('#html-source').val();
                            $('#content').val(content);
                        }
                    } else {
                        // When Quill is available, use either Quill or HTML source
                        if (mode === 'visual' && window.quill) {
                            // Use Quill content
                            content = window.quill.root.innerHTML;
                            $('#content').val(content);
                        } else {
                            // Use HTML source content
                            content = $('#html-source').val();
                            $('#content').val(content);
                        }
                    }

                    // Extract variables from content and update the variables input fields
                    const extractedVariables = extractVariablesFromContent(content);

                    // Remove all existing variable fields
                    $('.variable-field').remove();

                    // Add extracted variables to the form
                    if (extractedVariables.length > 0) {
                        // Create a variable field for each extracted variable
                        for (let i = 0; i < extractedVariables.length; i++) {
                            // Clone the template variable field
                            const variableField = $('#variable-field-template').clone();
                            variableField.removeAttr('id');
                            variableField.addClass('variable-field');
                            variableField.find('input').val(extractedVariables[i]);
                            $('#variablesContainer').append(variableField);
                        }
                    }

                    console.log('Form submitted with content from ' + (window.quillNotAvailable ? 'fallback ' : '') + mode + ' editor');
                    console.log('Extracted variables:', extractedVariables);
                });

                // Add a small delay to ensure Quill is fully rendered
                setTimeout(function() {
                    if ($('.ql-toolbar').length === 0 || $('.ql-editor').length === 0) {
                        console.error('Quill toolbar or editor not found. Showing fallback textarea.');
                        $('#fallback-content').show();
                        $('#editor-container').hide();

                        // Set the fallback textarea as the active content field
                        $('#fallback-content').attr('name', 'content');
                        $('#content').attr('name', '_content_hidden');

                        // Set a flag to indicate Quill is not properly initialized
                        window.quillNotAvailable = true;
                    }
                }, 500);
            } catch (e) {
                console.error('Error initializing Quill:', e);
                $('#fallback-content').show();
                $('#editor-container').hide();

                // Set the fallback textarea as the active content field
                $('#fallback-content').attr('name', 'content');
                $('#content').attr('name', '_content_hidden');

                // Set a flag to indicate Quill initialization failed
                window.quillNotAvailable = true;
            }
        }

        // Add variable button
        $('#addVariableBtn').on('click', function() {
            const variablesContainer = $('#variablesContainer');

            // Clone the template variable field
            const variableField = $('#variable-field-template').clone();
            variableField.removeAttr('id');
            variableField.addClass('variable-field');
            variableField.css('display', 'flex');

            variablesContainer.append(variableField);

            // Focus the new input field
            variableField.find('input').focus();

            // Update the variables list
            updateVariablesList();

            console.log('Variable field added:', variableField);
        });

        // Remove variable button
        $(document).on('click', '.remove-variable', function() {
            $(this).closest('.variable-field').remove();
            updateVariablesList();
        });

        // Insert variable from input field
        $(document).on('click', '.insert-variable-from-input', function() {
            const input = $(this).closest('.variable-field').find('input');
            const variable = input.val().trim();

            if (variable) {
                insertVariableIntoEditor(variable);
            } else {
                // Alert user to enter a variable name first
                alert('Please enter a variable name first');
                input.focus();
            }
        });

        // Function to safely check if Quill is available
        function isQuillAvailable() {
            return !window.quillNotAvailable && typeof window.quill !== 'undefined' && window.quill;
        }

        // Function to insert variable into editor
        function insertVariableIntoEditor(variable) {

            const variableText = '@{{' + variable + '}}';
            const mode = $('input[name="editorMode"]:checked').val();

            // Determine which editor to use based on mode and Quill availability
            if (mode === 'html' || (window.quillNotAvailable && mode === 'visual')) {
                // Insert into HTML source or fallback textarea
                const textarea = mode === 'html' ? document.getElementById('html-source') : document.getElementById('fallback-content');
                if (!textarea) {
                    console.error('Target textarea not found');
                    return;
                }

                const startPos = textarea.selectionStart;
                const endPos = textarea.selectionEnd;
                const scrollTop = textarea.scrollTop;

                // Insert the variable at cursor position or at the end
                if (typeof startPos !== 'undefined') {
                    const text = textarea.value;
                    textarea.value = text.substring(0, startPos) + variableText + text.substring(endPos, text.length);
                    textarea.focus();
                    textarea.selectionStart = startPos + variableText.length;
                    textarea.selectionEnd = startPos + variableText.length;
                } else {
                    textarea.value += variableText;
                }

                // Restore scroll position
                textarea.scrollTop = scrollTop;

                console.log('Variable inserted into ' + (mode === 'html' ? 'HTML source' : 'fallback') + ' textarea:', variableText);
            } else if (isQuillAvailable()) {
                // Insert into Quill editor
                const selection = window.quill.getSelection();

                if (selection) {
                    // Insert the variable text at the cursor position using dangerouslyPasteHTML
                    window.quill.deleteText(selection.index, selection.length);
                    window.quill.clipboard.dangerouslyPasteHTML(selection.index, variableText);
                } else {
                    // Insert the variable text at the end of the document using dangerouslyPasteHTML
                    window.quill.clipboard.dangerouslyPasteHTML(window.quill.getLength() - 1, variableText);
                }

                console.log('Variable inserted into Quill editor:', variableText);
            } else {
                console.error('No suitable editor found for variable insertion');
            }
        }

        // Function to update the variables list
        function updateVariablesList() {
            const variablesList = $('#variablesList .d-flex');
            variablesList.empty();

            // Get all variables from input fields
            const variables = [];
            $('#variablesContainer input').each(function() {
                const value = $(this).val().trim();
                if (value && !variables.includes(value)) {
                    variables.push(value);
                }
            });

            // Add buttons for each variable
            variables.forEach(function(variable) {
                const button = $('<button>')
                    .addClass('btn btn-sm btn-outline-secondary mb-1 me-1')
                    .attr('type', 'button')
                    .attr('data-variable', variable)
                    .text(variable)
                    .on('click', function() {
                        insertVariableIntoEditor(variable);
                    });

                variablesList.append(button);
            });

            // Show/hide the variables list section based on whether there are variables
            if (variables.length > 0) {
                $('#variablesList').show();
            } else {
                $('#variablesList').hide();
            }
        }

        // Initialize variables list
        updateVariablesList();

        // Update variables list when inputs change
        $(document).on('input', '#variablesContainer input', function() {
            updateVariablesList();
        });

        // Insert variable into editor when clicking on a variable button
        $(document).on('click', '.insert-variable', function() {
            const variable = $(this).data('variable');

            // Use our helper function to insert the variable
            insertVariableIntoEditor(variable);
        });

        // Initialize form validation
        const formValidation = FormValidation.formValidation(document.getElementById('templateForm'), {
            fields: {
                name: {
                    validators: {
                        notEmpty: {
                            message: 'Please enter template name'
                        },
                        stringLength: {
                            min: 2,
                            max: 255,
                            message: 'Template name must be between 2 and 255 characters'
                        }
                    }
                },
                subject: {
                    validators: {
                        notEmpty: {
                            message: 'Please enter email subject'
                        },
                        stringLength: {
                            min: 2,
                            max: 255,
                            message: 'Email subject must be between 2 and 255 characters'
                        }
                    }
                }
            },
            plugins: {
                trigger: new FormValidation.plugins.Trigger(),
                bootstrap5: new FormValidation.plugins.Bootstrap5({
                    eleValidClass: '',
                    rowSelector: '.mb-3'
                }),
                submitButton: new FormValidation.plugins.SubmitButton(),
                autoFocus: new FormValidation.plugins.AutoFocus()
            }
        }).on('core.form.valid', function() {
            // Get the current editor mode
            const mode = $('input[name="editorMode"]:checked').val();

            // Update hidden input with content from the active editor
            if (window.quillNotAvailable) {
                // When Quill is not available, use either fallback textarea or HTML source
                if (mode === 'visual') {
                    // Use fallback textarea content
                    $('#content').val($('#fallback-content').val());
                } else {
                    // Use HTML source content
                    $('#content').val($('#html-source').val());
                }
            } else {
                // When Quill is available, use either Quill or HTML source
                if (mode === 'visual' && window.quill) {
                    // Use Quill content
                    $('#content').val(window.quill.root.innerHTML);
                } else {
                    // Use HTML source content
                    $('#content').val($('#html-source').val());
                }
            }

            // Submit the form when it's valid
            document.getElementById('templateForm').submit();
        });

        // Common variables dropdown handler
        $('#commonVariables').on('change', function() {
            const variable = $(this).val();
            if (variable) {
                // Use our helper function to insert the variable
                insertVariableIntoEditor(variable);

                // Reset the select dropdown
                $(this).val('');
            }
        });
    });
});
</script>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Edit Email Template</h5>
                <a href="{{ route('email-templates.index') }}" class="btn btn-secondary">
                    <i class="ti ti-arrow-left me-1"></i> Back to List
                </a>
            </div>
            <div class="card-body">
                <form id="templateForm" action="{{ route('email-templates.update', $emailTemplate->id) }}" method="POST">
                    @csrf
                    @method('PUT')

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Template Name</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $emailTemplate->name) }}" placeholder="Enter template name" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="subject" class="form-label">Email Subject</label>
                                <input type="text" class="form-control @error('subject') is-invalid @enderror" id="subject" name="subject" value="{{ old('subject', $emailTemplate->subject) }}" placeholder="Enter email subject" required>
                                @error('subject')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select @error('category') is-invalid @enderror" id="category" name="category">
                                    <option value="">Select a category</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->value }}" {{ old('category', $emailTemplate->category?->value) == $category->value ? 'selected' : '' }}>
                                            {{ $category->label() }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('category')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Template Variables</label>
                            <!-- Template for variable field (hidden) -->
                            <div id="variable-field-template" class="input-group mb-2" style="display: none;">
                                <input type="text" class="form-control" name="variables[]" placeholder="Variable name (without @{{ }})">
                                <button type="button" class="btn btn-outline-primary insert-variable-from-input" title="Insert this variable into the content">
                                    <i class="ti ti-arrow-right"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger remove-variable">
                                    <i class="ti ti-x"></i>
                                </button>
                            </div>

                            <div id="variablesContainer">
                                @if($emailTemplate->variables && count($emailTemplate->getFormattedVariables()) > 0)
                                    @foreach($emailTemplate->getFormattedVariables() as $variable)
                                        <div class="input-group mb-2 variable-field">
                                            <input type="text" class="form-control" name="variables[]" value="{!! $variable !!}" placeholder="Variable name (without {{ }})">
                                            <button type="button" class="btn btn-outline-primary insert-variable-from-input" title="Insert this variable into the content">
                                                <i class="ti ti-arrow-right"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger remove-variable">
                                                <i class="ti ti-x"></i>
                                            </button>
                                        </div>
                                    @endforeach
                                @endif
                            </div>
                            <button type="button" id="addVariableBtn" class="btn btn-outline-primary btn-sm mt-2">
                                <i class="ti ti-plus me-1"></i> Add Variable
                            </button>

                            <div id="variablesList" class="mt-3">
                                <label class="form-label">Click to insert variable:</label>
                                <div class="d-flex flex-wrap gap-1">
                                    <!-- Variables will be added here dynamically -->
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">Common Variables</label>
                            <select id="commonVariables" class="form-select">
                                <option value="">Select a variable to insert</option>
                                <option value="email">email</option>
                                <option value="hotel">hotel</option>
                                <option value="name">name</option>
                                <option value="date">date</option>
                                <option value="company">company</option>
                            </select>
                            <small class="text-muted">Select a variable to insert it at the cursor position</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label for="editor-container" class="form-label mb-0">Email Content</label>
                            <div class="btn-group" role="group">
                                <input type="radio" class="btn-check" name="editorMode" id="visualMode" value="visual" checked>
                                <label class="btn btn-outline-primary btn-sm" for="visualMode">Visual Editor</label>
                                <input type="radio" class="btn-check" name="editorMode" id="htmlMode" value="html">
                                <label class="btn btn-outline-primary btn-sm" for="htmlMode">HTML Source</label>
                            </div>
                        </div>
                        <div class="alert alert-info mb-2">
                            <i class="ti ti-info-circle me-1"></i>
                            You can switch between <strong>Visual Editor</strong> and <strong>HTML Source</strong> modes to edit the email template.
                        </div>

                        <!-- Quill editor container with explicit border -->
                        <div id="editor-container" style="height: 300px; border: 1px solid #d8d6de; border-radius: 0.375rem;"></div>

                        <!-- HTML source textarea (initially hidden) -->
                        <textarea id="html-source" class="form-control" style="height: 300px; display: none;">{{ old('content', $emailTemplate->content) }}</textarea>

                        <!-- Fallback textarea that will be hidden if Quill loads properly -->
                        <textarea id="fallback-content" name="_fallback_content" class="form-control mt-2" style="height: 300px; display: none;">{{ old('content', $emailTemplate->content) }}</textarea>

                        <!-- Hidden input for Quill content -->
                        <input type="hidden" id="content" name="content" value="{{ old('content', $emailTemplate->content) }}">

                        @error('content')
                            <div class="text-danger mt-1">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Add some CSS to ensure Quill editor is visible -->
                    <style>
                        .ql-toolbar.ql-snow {
                            border: 1px solid #d8d6de;
                            border-top-left-radius: 0.375rem;
                            border-top-right-radius: 0.375rem;
                            background-color: #f8f8f8;
                            padding: 8px;
                        }
                        .ql-container.ql-snow {
                            border: 1px solid #d8d6de;
                            border-bottom-left-radius: 0.375rem;
                            border-bottom-right-radius: 0.375rem;
                            min-height: 200px;
                        }
                        .ql-editor {
                            min-height: 200px;
                            background-color: #fff;
                            font-family: 'Helvetica Neue', Arial, sans-serif;
                            font-size: 14px;
                            line-height: 1.5;
                            padding: 12px 15px;
                        }
                        /* Make sure the editor is visible */
                        #editor-container {
                            display: block;
                            /*display: block !important;*/
                            visibility: visible !important;
                        }
                    </style>

                    <!-- Add a script to check if Quill is properly loaded -->
                    <script>
                        // Function to safely check if jQuery is available
                        function checkJQuery() {
                            if (typeof jQuery === 'undefined') {
                                console.error('jQuery is not loaded. Showing fallback textarea without jQuery.');
                                document.getElementById('fallback-content').style.display = 'block';
                                if (document.getElementById('editor-container')) {
                                    document.getElementById('editor-container').style.display = 'none';
                                }
                                // Try again after a short delay
                                setTimeout(checkJQuery, 100);
                                return false;
                            }
                            return true;
                        }

                        // Ensure jQuery is available before executing code
                        document.addEventListener('DOMContentLoaded', function() {
                            // Check if jQuery is loaded
                            if (!checkJQuery()) {
                                return;
                            }

                            // Use MutationObserver instead of deprecated DOMNodeInserted event
                            // This will monitor changes to the DOM and can be used to detect when Quill is initialized
                            const targetNode = document.getElementById('editor-container');
                            if (targetNode) {
                                const observer = new MutationObserver(function(mutations) {
                                    // Check if Quill is properly initialized
                                    const qlEditor = document.querySelector('.ql-editor');
                                    const qlToolbar = document.querySelector('.ql-toolbar');

                                    if (qlEditor && qlToolbar) {
                                        console.log('Quill editor properly initialized (detected by MutationObserver)');
                                        // Disconnect the observer once Quill is initialized
                                        observer.disconnect();
                                    }
                                });

                                // Start observing the target node for configured mutations
                                observer.observe(targetNode, { 
                                    childList: true,  // observe direct children
                                    subtree: true,    // and lower descendants too
                                    attributes: true  // observe attribute changes
                                });
                            }

                            // Check if Quill editor is properly loaded after a delay
                            setTimeout(function() {
                                // If we already know Quill is not available, don't bother checking
                                if (window.quillNotAvailable === true) {
                                    console.log('Quill already marked as not available, using fallback textarea');
                                    return;
                                }

                                // Check for global Quill instance first
                                if (typeof window.quill !== 'undefined' && window.quill) {
                                    console.log('Quill editor properly initialized (global instance found)');
                                    return;
                                }

                                const editorContainer = document.getElementById('editor-container');
                                const qlEditor = document.querySelector('.ql-editor');
                                const qlToolbar = document.querySelector('.ql-toolbar');

                                if (!qlEditor || !qlToolbar) {
                                    console.error('Quill editor not properly initialized, showing fallback textarea');
                                    document.getElementById('fallback-content').style.display = 'block';
                                    if (editorContainer) {
                                        editorContainer.style.display = 'none';
                                    }

                                    // Set a flag to indicate Quill is not properly initialized
                                    window.quillNotAvailable = true;

                                    // Set the fallback textarea as the active content field
                                    if (document.getElementById('fallback-content')) {
                                        document.getElementById('fallback-content').setAttribute('name', 'content');
                                    }
                                    if (document.getElementById('content')) {
                                        document.getElementById('content').setAttribute('name', '_content_hidden');
                                    }
                                } else {
                                    console.log('Quill editor properly initialized (elements found)');
                                }
                            }, 1000);
                        });
                    </script>

                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary me-2">Update Template</button>
                        <a href="{{ route('email-templates.index') }}" class="btn btn-label-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
