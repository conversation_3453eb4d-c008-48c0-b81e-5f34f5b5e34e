@extends('layouts/layoutSavAdminMaster')

@section('title', 'Email Templates Management')

<!-- Vendor Styles -->
@section('vendor-style')
@vite([
'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
'resources/assets/vendor/libs/select2/select2.scss',
'resources/assets/vendor/libs/@form-validation/form-validation.scss',
'resources/assets/vendor/libs/animate-css/animate.scss',
'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

<!-- Vendor Scripts -->
@section('vendor-script')
@vite([
'resources/assets/vendor/libs/jquery/jquery.js',
'resources/assets/vendor/libs/moment/moment.js',
'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
'resources/assets/vendor/libs/select2/select2.js',
'resources/assets/vendor/libs/@form-validation/popular.js',
'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
'resources/assets/vendor/libs/@form-validation/auto-focus.js',
'resources/assets/vendor/libs/cleavejs/cleave.js',
'resources/assets/vendor/libs/cleavejs/cleave-phone.js',
'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

<!-- Page Scripts -->
@section('page-script')
<script>
    // Ensure jQuery is available before executing code
    document.addEventListener('DOMContentLoaded', function() {
        // Check if jQuery is loaded
        if (typeof jQuery === 'undefined') {
            console.error('jQuery is not loaded. Cannot initialize DataTable.');
            return;
        }

        // jQuery is available, proceed with initialization
        $(document).ready(function() {
            // Initialize DataTable
            const templatesTable = $('.datatables-templates').DataTable({
                processing: true,
                serverSide: false,
                // No AJAX, using server-side rendered data
                columns: [
                    { data: 'id', name: 'id' },
                    { data: 'name', name: 'name' },
                    { data: 'category', name: 'category' },
                    { data: 'subject', name: 'subject' },
                    { data: 'variables', name: 'variables', searchable: false },
                    { data: 'actions', name: 'actions', orderable: false, searchable: false }
                ]
            });

            // Add custom filtering for category
            $('#category-filter').on('change', function() {
                const categoryValue = $(this).val();

                // Custom filtering function
                $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
                    // If no category filter is selected, show all rows
                    if (!categoryValue) {
                        return true;
                    }

                    // Get the category cell content (3rd column, index 2)
                    const categoryCell = $(templatesTable.cell(dataIndex, 2).node());

                    // Check for "Uncategorized" filter
                    if (categoryValue === 'uncategorized') {
                        return categoryCell.find('.text-muted').length > 0;
                    }

                    // For regular categories, check if the badge contains the category value
                    const badge = categoryCell.find('.badge');
                    if (badge.length > 0) {
                        // We need to check if the category matches, but we only have the label in the DOM
                        // So we'll check if any of the enum cases with this value has a matching label
                        const categoryText = badge.text().trim();

                        // Find the category with the selected value
                        const matchingCategory = @json($categories)
                            .find(cat => cat['value'] === categoryValue);

                        if (matchingCategory) {
                            return categoryText === matchingCategory['label'];
                        }
                    }

                    return false;
                });

                // Apply the filter
                templatesTable.draw();

                // Remove the custom filtering function after drawing
                $.fn.dataTable.ext.search.pop();
            });

            // Handle delete confirmation
            $(document).on('click', '.delete-record', function(e) {
                e.preventDefault();
                const form = $(this).closest('form');

                Swal.fire({
                    title: 'Are you sure?',
                    text: "You won't be able to revert this!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, delete it!',
                    customClass: {
                        confirmButton: 'btn btn-primary me-3',
                        cancelButton: 'btn btn-label-secondary'
                    },
                    buttonsStyling: false
                }).then(function (result) {
                    if (result.value) {
                        form.submit();
                    }
                });
            });
        });
    });
</script>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center row">
                    <div class="col-md-4">
                        <h5 class="mb-0">Email Templates</h5>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <label for="category-filter" class="me-2">Filter by Category:</label>
                            <select id="category-filter" class="form-select">
                                <option value="">All Categories</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category['value'] }}">{{ $category['label'] }}</option>
                                @endforeach
                                <option value="uncategorized">Uncategorized</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ route('email-templates.create') }}" class="btn btn-primary">
                            <i class="ti ti-plus me-1"></i> Add New Template
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-datatable table-responsive">
                <table class="datatables-templates table">
                    <thead class="border-top">
                        <tr>
                            <th>ID</th>
                            <th>Template Name</th>
                            <th>Category</th>
                            <th>Subject</th>
                            <th>Variables</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($templates as $template)
                        <tr>
                            <td>{{ $template->id }}</td>
                            <td>{{ $template->name }}</td>
                            <td>
                                @if($template->category)
                                    <span class="badge bg-label-primary">{{ $template->category->label() }}</span>
                                @else
                                    <span class="text-muted">Uncategorized</span>
                                @endif
                            </td>
                            <td>{{ $template->subject }}</td>
                            <td>
                                @if($template->variables)
                                    @foreach($template->getFormattedVariables() as $variable)
                                        <span class="badge bg-primary me-1">{{ $variable }}</span>
                                    @endforeach
                                @else
                                    <span class="text-muted">No variables</span>
                                @endif
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <a href="{{ route('email-templates.show', $template->id) }}" class="btn btn-sm btn-icon">
                                        <i class="ti ti-eye text-primary"></i>
                                    </a>
                                    <a href="{{ route('email-templates.edit', $template->id) }}" class="btn btn-sm btn-icon">
                                        <i class="ti ti-edit text-primary"></i>
                                    </a>
                                    <form action="{{ route('email-templates.duplicate', $template->id) }}" method="POST" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-sm btn-icon" title="Duplicate">
                                            <i class="ti ti-copy text-info"></i>
                                        </button>
                                    </form>
                                    <form action="{{ route('email-templates.destroy', $template->id) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-icon delete-record">
                                            <i class="ti ti-trash text-danger"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@if(session('success'))
<div class="alert alert-success alert-dismissible fade show" role="alert">
    {{ session('success') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    {{ session('error') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif
@endsection
