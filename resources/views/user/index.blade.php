@extends('layouts/layoutSavAdminMaster')

@section('title', 'User Management')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
    <h4 class="fw-bold py-3 mb-4">
        <span class="text-muted fw-light">User Management /</span> Users
    </h4>

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Users</h5>
            <a href="{{ route('users.create') }}" class="btn btn-primary">
                <i class="ti ti-plus me-1"></i> Add New User
            </a>
        </div>
        <div class="card-body">
            @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif

            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Teams & Roles</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($users as $user)
                            <tr>
                                <td>
                                    @if($user->avatar)
                                        <img src="{{ asset('storage/' . $user->avatar) }}" alt="{{ $user->name }}" class="rounded-circle me-2" width="32" height="32">
                                    @else
                                        <span class="avatar-initial rounded-circle bg-label-primary me-2">{{ substr($user->name, 0, 1) }}</span>
                                    @endif
                                    {{ $user->name }}
                                </td>
                                <td>{{ $user->email }}</td>
                                <td>
                                    @if($user->teams->count() > 0)
                                        @foreach($user->teams as $team)
                                            <div class="mb-2">
                                                <strong class="d-block mb-1">{{ $team->name }}</strong>
                                                @php
                                                    $teamRoles = $user->roleAssignments->where('team_id', $team->id)->map(function($ra) {
                                                        return $ra->role;
                                                    });
                                                @endphp
                                                @foreach($teamRoles as $role)
                                                    <span class="badge bg-primary me-1">{{ $role->name }}</span>
                                                @endforeach
                                            </div>
                                        @endforeach
                                    @else
                                        <div class="text-muted">No teams assigned</div>
                                    @endif
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                                            <i class="ti ti-dots-vertical"></i>
                                        </button>
                                        <div class="dropdown-menu">
                                            <a class="dropdown-item" href="{{ route('users.edit', $user->id) }}">
                                                <i class="ti ti-pencil me-1"></i> Edit
                                            </a>
                                            @if(auth()->user()->hasRole('master-admin'))
                                                <a class="dropdown-item" href="{{ route('users.impersonate', $user->id) }}">
                                                    <i class="ti ti-user-check me-1"></i> Impersonate
                                                </a>
                                            @endif
                                            <form action="{{ route('users.destroy', $user->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="dropdown-item" onclick="return confirm('Are you sure you want to delete this user?')">
                                                    <i class="ti ti-trash me-1"></i> Delete
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection
