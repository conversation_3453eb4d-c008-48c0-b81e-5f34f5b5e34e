@php
  $customizerHidden = 'customizer-hide';
@endphp

@extends('layouts.blankLayout')

@section('title', 'Login')

@section('vendor-style')
  @vite([
    'resources/assets/vendor/libs/@form-validation/form-validation.scss'
  ])
@endsection

@section('page-style')
  @vite([
    'resources/assets/vendor/scss/pages/page-auth.scss'
  ])

  <style>
    .social-btn {
      display: flex;
      align-items: center;
      color: white;
      padding: 8px 15px;
      text-decoration: none;
      border-radius: 4px;
      font-family: sans-serif;
      margin-bottom: 10px;
      width: 250px;
      justify-content: flex-start;
    }
    .social-btn:hover {
      opacity: 0.9;
      color: white;
    }
    .btn-microsoft {
      background-color: #2F2F2F;
    }
    .btn-microsoft:hover {
      background-color: #1d1d1d;
    }
    .btn-google {
      background-color: #DB4437;
    }
    .btn-google:hover {
      background-color: #C53929;
    }
    .btn-facebook {
      background-color: #4267B2;
    }
    .btn-facebook:hover {
      background-color: #365899;
    }
    .btn-twitter {
      background-color: #1DA1F2;
    }
    .btn-twitter:hover {
      background-color: #0C85D0;
    }
  </style>
@endsection

@section('vendor-script')
  @vite([
    'resources/assets/vendor/libs/@form-validation/popular.js',
    'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
    'resources/assets/vendor/libs/@form-validation/auto-focus.js'
  ])
@endsection

@section('page-script')
  @vite([
    'resources/assets/js/pages-auth.js'
  ])
@endsection

@section('content')
  <div class="container-xxl">
    <div class="authentication-wrapper authentication-basic container-p-y">
      <div class="authentication-inner py-6">
        <!-- Login -->
        <div class="card">
          <div class="card-body">
            <!-- Logo -->
            <div class="app-brand justify-content-center mb-6">
              <a href="{{url('/')}}" class="app-brand-link">
                <span class="app-brand-logo demo">@include('_partials.macros',['height'=>20,'withbg' => "fill: #fff;"])</span>
                <span class="app-brand-text demo text-heading fw-bold">{{ config('variables.templateName') }}</span>
              </a>
            </div>
            <!-- /Logo -->
            <h4 class="mb-1">Bun venit la {{ config('variables.templateName') }}! 👋</h4>
            <p class="mb-6">Autentifica-te si hai sa incepem o noua aventura!</p>

            @if($errors->any())
              <div class="alert alert-danger mb-4">
                @foreach($errors->all() as $error)
                  <div>{{ $error }}</div>
                @endforeach
              </div>
            @endif

            @if(session('error'))
              <div class="alert alert-danger mb-4">
                {{ session('error') }}
              </div>
            @endif

            <form id="formAuthentication" class="mb-4" action="{{ route('login') }}" method="POST">

              @csrf

              <div class="mb-6">
                <label for="email" class="form-label">Email sau nume de utilizator</label>
                <input type="text" class="form-control" id="email" name="email"
                       placeholder="Introduceți email sau nume de utilizator" autofocus>
              </div>
              <div class="mb-6 form-password-toggle">
                <label class="form-label" for="password">Parola</label>
                <div class="input-group input-group-merge">
                  <input type="password" id="password" class="form-control" name="password"
                         placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                         aria-describedby="password" />
                  <span class="input-group-text cursor-pointer"><i class="ti ti-eye-off"></i></span>
                </div>
              </div>
              <div class="my-8">
                <div class="d-flex justify-content-between">
                  <div class="form-check mb-0 ms-2">
                    <input class="form-check-input" type="checkbox" id="remember-me">
                    <label class="form-check-label" for="remember-me">
                      Aminteste-ti de mine
                    </label>
                  </div>
                  <a href="javascript:void(0);">
                    <p class="mb-0">Ai uitat parola?</p>
                  </a>
                </div>
              </div>
              <div class="mb-6">
                <button class="btn btn-primary d-grid w-100" type="submit">Autentificare</button>
              </div>
            </form>

            <div class="divider my-6">
              <div class="divider-text">sau</div>
            </div>

            <div class="d-flex flex-column align-items-center">
{{--              <a href="{{ url('/auth/microsoft') }}" class="social-btn btn-microsoft">--}}
              <a href="{{ route('admin.oauth.microsoft') }}" class="social-btn btn-microsoft">
                <i class="fa-brands fa-microsoft me-3"></i>
                Microsoft login
              </a>

              <a href="{{ url('/auth/google') }}" class="social-btn btn-google">
                <i class="fa-brands fa-google me-3"></i>
                Google login
              </a>

              <a href="{{ url('/auth/facebook') }}" class="social-btn btn-facebook">
                <i class="fa-brands fa-facebook me-3"></i>
                Facebook login
              </a>

              <a href="{{ url('/auth/twitter') }}" class="social-btn btn-twitter">
                <i class="fa-brands fa-twitter me-3"></i>
                Twitter login
              </a>
            </div>

            @if (false)
              <p class="text-center">
                <span>New on our platform?</span>
                <a href="{{url('auth/register-basic')}}">
                  <span>Create an account</span>
                </a>
              </p>
            @endif
          </div>
        </div>
        <!-- /Register -->
      </div>
    </div>
  </div>
@endsection
