@php
  $customizerHidden = 'customizer-hide';
@endphp

@extends('layouts.blankLayout')

@section('title', 'Verificare Email')

@section('vendor-style')
  @vite([
    'resources/assets/vendor/libs/@form-validation/form-validation.scss'
  ])
@endsection

@section('page-style')
  @vite([
    'resources/assets/vendor/scss/pages/page-auth.scss'
  ])
@endsection

@section('vendor-script')
  @vite([
    'resources/assets/vendor/libs/@form-validation/popular.js',
    'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
    'resources/assets/vendor/libs/@form-validation/auto-focus.js'
  ])
@endsection

@section('page-script')
  @vite([
    'resources/assets/js/pages-auth.js'
  ])
@endsection

@section('content')
  <div class="container-xxl">
    <div class="authentication-wrapper authentication-basic container-p-y">
      <div class="authentication-inner py-6">
        <!-- Verify Email -->
        <div class="card">
          <div class="card-body">
            <!-- Logo -->
            <div class="app-brand justify-content-center mb-6">
              <a href="{{url('/')}}" class="app-brand-link">
                <span class="app-brand-logo demo">@include('_partials.macros',['height'=>20,'withbg' => "fill: #fff;"])</span>
                <span class="app-brand-text demo text-heading fw-bold">{{ config('variables.templateName') }}</span>
              </a>
            </div>
            <!-- /Logo -->
            <h4 class="mb-1">Verifică adresa de email ✉️</h4>
            <p class="mb-6">
              Un link de verificare a fost trimis la adresa ta de email. Te rugăm să verifici căsuța de email și să accesezi link-ul pentru a-ți confirma adresa.
            </p>

            @if (session('status') == 'verification-link-sent')
              <div class="alert alert-success mb-6" role="alert">
                Un nou link de verificare a fost trimis la adresa de email pe care ai furnizat-o în setările profilului tău.
              </div>
            @endif

            <form id="formAuthentication" class="mb-4" action="{{ route('verification.send') }}" method="POST">
              @csrf
              <div class="mb-6">
                <button class="btn btn-primary d-grid w-100" type="submit">Retrimite email de verificare</button>
              </div>
            </form>

            <div class="text-center">
              <a href="{{ route('profile.edit') }}" class="d-flex align-items-center justify-content-center">
                <i class="ti ti-chevron-left scaleX-n1-rtl me-1"></i>
                Înapoi la profil
              </a>
            </div>
          </div>
        </div>
        <!-- /Verify Email -->
      </div>
    </div>
  </div>
@endsection