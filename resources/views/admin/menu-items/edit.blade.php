@extends('layouts.contentSavAdminNavbarLayout')

@section('title', 'Edit Menu Item')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/select2/select2.css'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/select2/select2.js'
])
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Edit Menu Item</h5>
                <a href="{{ route('admin.menu-items.index') }}" class="btn btn-secondary">
                    <i class="ti ti-arrow-left me-1"></i> Back to List
                </a>
            </div>
            <div class="card-body">
                @if($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
                @endif

                <form action="{{ route('admin.menu-items.update', $menuItem) }}" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ old('name', $menuItem->name) }}" required>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="url" class="form-label">URL</label>
                            <input type="text" class="form-control" id="url" name="url" value="{{ old('url', $menuItem->url) }}">
                            <small class="text-muted">Leave empty for parent menu items or headers</small>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="icon" class="form-label">Icon Class</label>
                            <input type="text" class="form-control" id="icon" name="icon" value="{{ old('icon', $menuItem->icon) }}">
                            <small class="text-muted">e.g., "menu-icon tf-icons ti ti-smart-home"</small>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="slug" class="form-label">Slug</label>
                            <input type="text" class="form-control" id="slug" name="slug" value="{{ old('slug', $menuItem->slug) }}">
                            <small class="text-muted">Used for determining active menu items</small>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="parent_id" class="form-label">Parent Menu Item</label>
                            <select class="form-select" id="parent_id" name="parent_id">
                                <option value="">None (Root Item)</option>
                                @foreach($parentMenuItems as $parentMenuItem)
                                <option value="{{ $parentMenuItem->id }}" {{ old('parent_id', $menuItem->parent_id) == $parentMenuItem->id ? 'selected' : '' }}>
                                    {{ $parentMenuItem->name }}
                                </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="order" class="form-label">Order</label>
                            <input type="number" class="form-control" id="order" name="order" value="{{ old('order', $menuItem->order) }}">
                        </div>

                        <div class="col-md-6 mb-3">
                            <div class="form-check form-switch mt-3">
                                <input class="form-check-input" type="checkbox" id="is_header" name="is_header" value="1" {{ old('is_header', $menuItem->is_header) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_header">Is Header</label>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <div class="form-check form-switch mt-3">
                                <input class="form-check-input" type="checkbox" id="active" name="active" value="1" {{ old('active', $menuItem->active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="active">Active</label>
                            </div>
                        </div>

                        <div class="col-12 mb-3">
                            <label for="roles" class="form-label">Roles <span class="text-danger">*</span></label>
                            <select class="select2 form-select" id="roles" name="roles[]" multiple required>
                                @foreach($roles as $role)
                                <option value="{{ $role->id }}" {{ in_array($role->id, old('roles', $selectedRoles)) ? 'selected' : '' }}>
                                    {{ $role->name }}
                                </option>
                                @endforeach
                            </select>
                            <small class="text-muted">Select which roles can see this menu item</small>
                        </div>

                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">Update Menu Item</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('page-script')
<script>
    // Ensure jQuery is available
    if (typeof jQuery !== 'undefined') {
        $(document).ready(function() {
            // Initialize Select2
            $('.select2').select2();

            // Toggle URL and slug fields based on is_header
            $('#is_header').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#url').val('').prop('disabled', true);
                    $('#slug').val('').prop('disabled', true);
                } else {
                    $('#url').prop('disabled', false);
                    $('#slug').prop('disabled', false);
                }
            });

            // Trigger change event on page load
            $('#is_header').trigger('change');
        });
    } else {
        console.error('jQuery is not defined. Please check that jQuery is loaded properly.');
    }
</script>
@endsection
