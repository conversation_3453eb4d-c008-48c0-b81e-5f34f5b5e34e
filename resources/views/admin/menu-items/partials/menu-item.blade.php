<li class="dd-item" data-id="{{ $menuItem->id }}">
    <div class="dd-handle">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                @if($menuItem->icon)
                    <i class="{{ $menuItem->icon }} me-2"></i>
                @endif
                <span class="{{ $menuItem->is_header ? 'fw-bold' : '' }}">{{ $menuItem->name }}</span>
                @if(!$menuItem->active)
                    <span class="badge bg-warning ms-1">Inactive</span>
                @endif
            </div>
            <div class="dd-nodrag">
                <a href="{{ route('admin.menu-items.edit', $menuItem) }}" class="btn btn-sm btn-icon btn-primary">
                    <i class="ti ti-pencil"></i>
                </a>
                <form action="{{ route('admin.menu-items.destroy', $menuItem) }}" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-sm btn-icon btn-danger" onclick="return confirm('Are you sure you want to delete this menu item?')">
                        <i class="ti ti-trash"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
    @if($menuItem->children->count() > 0)
        <ol class="dd-list">
            @foreach($menuItem->children as $child)
                @include('admin.menu-items.partials.menu-item', ['menuItem' => $child])
            @endforeach
        </ol>
    @endif
</li>