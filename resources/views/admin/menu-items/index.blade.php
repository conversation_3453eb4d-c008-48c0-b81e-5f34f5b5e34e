@extends('layouts.contentSavAdminNavbarLayout')

@section('title', 'Menu Management')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/nestable2/jquery.nestable.min.css'
])
@endsection

@section('vendor-script')
@vite([

	'resources/assets/vendor/libs/jquery/jquery.js',
    'resources/assets/vendor/libs/moment/moment.js',
    'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
    'resources/assets/vendor/libs/select2/select2.js',
    'resources/assets/vendor/libs/@form-validation/popular.js',
    'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
    'resources/assets/vendor/libs/@form-validation/auto-focus.js',
    'resources/assets/vendor/libs/cleavejs/cleave.js',
    'resources/assets/vendor/libs/cleavejs/cleave-phone.js',
    'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
    'resources/assets/vendor/libs/nouislider/nouislider.js',



//  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/nestable2/jquery.nestable.min.js'




])
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Menu Management</h5>
                <a href="{{ route('admin.menu-items.create') }}" class="btn btn-primary">
                    <i class="ti ti-plus me-1"></i> Add Menu Item
                </a>
            </div>
            <div class="card-body">
                @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
                @endif

                <p class="text-muted mb-3">Drag and drop menu items to reorder them. Click on the edit button to modify a menu item.</p>

                <div class="dd" id="nestable">
                    <ol class="dd-list">
                        @foreach($menuItems as $menuItem)
                            @include('admin.menu-items.partials.menu-item', ['menuItem' => $menuItem])
                        @endforeach
                    </ol>
                </div>

                <div class="mt-3">
                    <button type="button" class="btn btn-primary" id="saveOrder">Save Order</button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('page-script')
<script>
    // Ensure jQuery is available
    if (typeof jQuery !== 'undefined') {
        $(document).ready(function() {
            // Initialize nestable
            $('#nestable').nestable({
                maxDepth: 3
            });

            // Save order
            $('#saveOrder').on('click', function() {
                const data = $('#nestable').nestable('serialize');
                const items = processItems(data);

                $.ajax({
                    url: '{{ route("admin.menu-items.reorder") }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        items: items
                    },
                    success: function(response) {
                        if (response.success) {
                            toastr.success('Menu order updated successfully');
                        } else {
                            toastr.error('Error updating menu order');
                        }
                    },
                    error: function() {
                        toastr.error('Error updating menu order');
                    }
                });
            });

            // Process items for saving
            function processItems(items, parentId = null, order = 0) {
                let result = [];

                items.forEach((item, index) => {
                    result.push({
                        id: item.id,
                        parent_id: parentId,
                        order: index
                    });

                    if (item.children && item.children.length > 0) {
                        result = result.concat(processItems(item.children, item.id));
                    }
                });

                return result;
            }
        });
    } else {
        console.error('jQuery is not defined. Please check that jQuery is loaded properly.');
    }
</script>
@endsection
