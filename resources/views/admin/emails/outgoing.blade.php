@extends('layouts/layoutSavAdminMaster')

@section('title', 'Outgoing Emails')

@section('vendor-style')
<!-- Vendor CSS -->
@vite([
'resources/assets/vendor/libs/select2/select2.scss'
])
@endsection

@section('vendor-script')
<!-- Vendor JS -->
@vite([
'resources/assets/vendor/libs/jquery/jquery.js',
'resources/assets/vendor/libs/select2/select2.js'
])
@endsection

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
    <h4 class="fw-bold py-3 mb-4">
        <span class="text-muted fw-light">Email Management /</span> Outgoing Emails
    </h4>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Filter Emails</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.outgoing-emails.index') }}">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Message ID</label>
                        <input type="text" class="form-control" name="message_id" value="{{ request('message_id') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Action</label>
                        <select class="select2" name="action_id" id="action-select">
                            <option value="">All Actions</option>
                            @if(request('action_id'))
                                <option value="{{ request('action_id') }}" selected>Loading...</option>
                            @endif
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Hotel</label>
                        <select class="select2" name="hotel_id" id="hotel-select">
                            <option value="">All Hotels</option>
                            @if(request('hotel_id'))
                                <option value="{{ request('hotel_id') }}" selected>Loading...</option>
                            @endif
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Subject</label>
                        <input type="text" class="form-control" name="subject" value="{{ request('subject') }}">
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">Filter</button>
                        <a href="{{ route('admin.outgoing-emails.index') }}" class="btn btn-secondary">Reset</a>
                    </div>
                </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Search Email Thread</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.outgoing-emails.conversation') }}">
                        <div class="mb-3">
                            <label class="form-label">Conversation ID</label>
                            <input type="text" class="form-control" name="conversation_id" required>
                            <div class="form-text">Enter a conversation ID to search for all emails in the thread</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Or Filter by Action</label>
                            <select class="select2" id="action-select-search" name="action_id">
                                <option value="">Select an Action</option>
                            </select>
                            <div class="form-text">Select an action to filter emails</div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="ti ti-search me-1"></i> Search Thread
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-title">Outgoing Emails</h5>
        </div>
        <div class="table-responsive text-nowrap">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Hotel</th>
                        <th>Action name</th>
                        <th>Subject</th>
                        <th>Sent At</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody class="table-border-bottom-0">
                    @forelse($emails as $email)
                    <tr>
                        <td>{{ $email->id }}</td>
                        <td>{{ $email->hotel->name ?? 'N/A' }}</td>
                        <td>{{ $email->action->name ?? 'N/A' }}</td>
                        <td>{{ $email->subject }}</td>
                        <td>
                            {{ $email->sent_at ? $email->sent_at->format('Y-m-d H:i:s') : 'Not sent' }}
                            @if($email->user && $email->sent_at)
                                <br><small class="text-muted">{{ \Illuminate\Support\Str::limit($email->user->email, 17) }}</small>
                            @endif
                        </td>
                        <td>
                            @if($email->status === 'sent')
                                <span class="badge bg-success">Sent</span>
                            @elseif($email->status === 'failed')
                                <span class="badge bg-danger" title="{{ $email->error_message }}">Failed</span>
                            @else
                                <span class="badge bg-warning">{{ ucfirst($email->status) }}</span>
                            @endif
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ route('admin.outgoing-emails.thread', $email->id) }}" class="btn btn-sm btn-info">
                                    <i class="ti ti-messages me-1"></i> View Thread
                                </a>
                                @if($email->conversation_id)
                                    <a href="{{ route('admin.outgoing-emails.conversation', ['conversation_id' => $email->conversation_id]) }}" class="btn btn-sm btn-primary">
                                        <i class="ti ti-search me-1"></i> Find All
                                    </a>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="text-center">No emails found</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="card-footer">
            {{ $emails->links() }}
        </div>
    </div>
</div>
@endsection

@section('page-script')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Ensure jQuery is loaded before executing code
        if (typeof jQuery === 'undefined') {
            console.error('jQuery is not loaded');
            return;
        }

        $(function () {
            // Initialize all select2 elements that don't have specific configurations
            $('.select2').not('#hotel-select, #action-select, #action-select-search').select2();

            // Initialize Select2 for hotels with minimum 3 characters for searching
            $('#hotel-select').select2({
                placeholder: 'Search for a hotel...',
                allowClear: true,
                minimumInputLength: 3,
                ajax: {
                    url: '{{ route("admin.outgoing-emails.hotels") }}',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            q: params.term
                        };
                    },
                    processResults: function (data) {
                        return data;
                    },
                    cache: true
                }
            });

            // Initialize Select2 for actions
            $('#action-select, #action-select-search').select2({
                placeholder: 'Search for an action...',
                allowClear: true,
                ajax: {
                    url: '{{ route("admin.outgoing-emails.actions") }}',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            q: params.term
                        };
                    },
                    processResults: function (data) {
                        return data;
                    },
                    cache: true
                }
            });

            // Load selected values if any
            @if(request('hotel_id'))
                $.ajax({
                    url: '{{ route("admin.outgoing-emails.hotels") }}',
                    data: { q: '', id: '{{ request('hotel_id') }}' },
                    dataType: 'json'
                }).then(function (data) {
                    if (data.results && data.results.length > 0) {
                        var option = new Option(data.results[0].text, data.results[0].id, true, true);
                        $('#hotel-select').append(option).trigger('change');
                    }
                });
            @endif

            @if(request('action_id'))
                $.ajax({
                    url: '{{ route("admin.outgoing-emails.actions") }}',
                    data: { q: '', id: '{{ request('action_id') }}' },
                    dataType: 'json'
                }).then(function (data) {
                    if (data.results && data.results.length > 0) {
                        var option = new Option(data.results[0].text, data.results[0].id, true, true);
                        $('#action-select').append(option).trigger('change');
                    }
                });
            @endif
        });
    });
</script>
@endsection
