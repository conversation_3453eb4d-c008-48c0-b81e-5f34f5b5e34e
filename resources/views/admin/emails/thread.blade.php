@extends('layouts/layoutSavAdminMaster')

@section('title', 'Email Thread')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
    <h4 class="fw-bold py-3 mb-4">
        <span class="text-muted fw-light">Email Management / <a href="{{ route('admin.outgoing-emails.index') }}">Outgoing Emails</a> /</span> Thread
    </h4>

    @if(isset($error))
        <div class="alert alert-danger">
            <h5>Error</h5>
            <p>{{ $error }}</p>
            <a href="{{ route('admin.oauth.microsoft') }}" class="btn btn-primary">Connect to Microsoft</a>
        </div>
    @endif

    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title">Original Email</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <strong>Action name:</strong> {{ $email->action->name ?? 'N/A' }}
                </div>
                <div class="col-md-6">
                    <strong>Sent:</strong> {{ $email->sent_at ? $email->sent_at->format('Y-m-d H:i:s') : 'Not sent' }}
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-12">
                    <strong>Subject:</strong> {{ $email->subject }}
                </div>
            </div>
            @if(auth()->user()->hasRole('master-adminx'))
                <div class="row mb-3">
                    <div class="col-md-12">
                        <strong>Message ID:</strong> {{ $email->internet_message_id }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-12">
                        <strong>Conversation ID:</strong> {{ $email->conversation_id }}
                    </div>
                </div>
            @endif
            <div class="row">
                <div class="col-md-12">
                    <strong>Content:</strong>
                    <div class="border p-3 mt-2">
                        {!! $email->body !!}
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if(auth()->user()->hasRole('master-adminx') && isset($rawApiResponse) && $rawApiResponse['success'])
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Microsoft Graph API Raw Response</h5>
                <button class="btn btn-sm btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#collapseApiResponse" aria-expanded="false" aria-controls="collapseApiResponse">
                    Toggle API Response
                </button>
            </div>
            <div class="collapse" id="collapseApiResponse">
                <div class="card-body">
                    <h6>Request URL:</h6>
                    <pre class="border p-2 bg-light"><code>{{ $rawApiResponse['url'] }}</code></pre>

                    <h6>Request Headers:</h6>
                    <pre class="border p-2 bg-light"><code>@foreach($rawApiResponse['headers'] as $key => $value){{ $key }}: {{ $value }}
@endforeach</code></pre>

                    <h6>Response Status: {{ $rawApiResponse['status'] }}</h6>

                    <h6>Response Body:</h6>
                    <pre class="border p-2 bg-light" style="max-height: 400px; overflow-y: auto;"><code>{{ json_encode($rawApiResponse['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) }}</code></pre>
                </div>
            </div>
        </div>
    @endif

    @if(isset($rawApiResponse) && $rawApiResponse['success'])
        @if(isset($rawApiResponse['response']['value']) && count($rawApiResponse['response']['value']) > 0)
            <h5 class="mb-3">Emails Threads ({{ count($rawApiResponse['response']['value']) }})</h5>

            @php
                // Sortăm emailurile după receivedDateTime în ordine descrescătoare
                $sortedMessages = collect($rawApiResponse['response']['value'])
                    ->sortByDesc(function ($message) {
                        return $message['receivedDateTime'] ?? '';
                    })
                    ->values()
                    ->all();
            @endphp

            @foreach($sortedMessages as $index => $message)
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">{{ $message['subject'] ?? 'No Subject' }}</h5>
                        <div>
                            <span class="badge bg-primary me-2">{{ \Carbon\Carbon::parse($message['receivedDateTime'])->format('Y-m-d H:i:s') }}</span>
                            @if(isset($message['webLink']))
                                <a href="{{ $message['webLink'] }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="ti ti-brand-microsoft me-1"></i> Open in Outlook
                                </a>
                            @endif
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>From:</strong> {{ $message['sender']['emailAddress']['name'] ?? '' }} ({{ $message['sender']['emailAddress']['address'] ?? '' }})
                            </div>
                            <div class="col-md-6">
                                @if(auth()->user()->hasRole('master-adminx'))
                                    @if(isset($message['parentFolderId']))
                                        <strong>Folder:</strong>
                                        @php
                                            $folderName = 'Unknown';
                                            if (isset($folders) && is_array($folders)) {
                                                foreach ($folders as $folder) {
                                                    if (isset($folder['id']) && $folder['id'] === $message['parentFolderId']) {
                                                        $folderName = $folder['displayName'];
                                                        break;
                                                    }
                                                }
                                            }
                                        @endphp
                                        <span class="badge bg-info">{{ $folderName }}</span>
                                    @endif
                                @endif
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <strong>To:</strong> 
                                @if(isset($message['toRecipients']) && is_array($message['toRecipients']))
                                    @foreach($message['toRecipients'] as $recipient)
                                        {{ $recipient['emailAddress']['address'] ?? '' }}{{ !$loop->last ? ', ' : '' }}
                                    @endforeach
                                @else
                                    N/A
                                @endif
                            </div>
                        </div>

                        @if(isset($message['ccRecipients']) && is_array($message['ccRecipients']) && count($message['ccRecipients']) > 0)
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <strong>CC:</strong> 
                                    @foreach($message['ccRecipients'] as $recipient)
                                        {{ $recipient['emailAddress']['address'] ?? '' }}{{ !$loop->last ? ', ' : '' }}
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        @if(auth()->user()->hasRole('master-adminx'))
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <strong>Message ID:</strong> <small>{{ $message['internetMessageId'] ?? 'N/A' }}</small>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <strong>ID:</strong> <small>{{ $message['id'] ?? 'N/A' }}</small>
                                </div>
                            </div>
                        @endif

                        <div class="row">
                            <div class="col-md-12">
                                <strong>Content:</strong>
                                <div class="border p-3 mt-2">
                                    {!! $message['body']['content'] ?? 'No content' !!}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer text-end">
                        @if(isset($message['webLink']))
                            @if($index === 0)
                                <a href="{{ $message['webLink'] }}" target="_blank" class="btn btn-primary">
                                    <i class="ti ti-brand-microsoft me-1"></i> Open in Outlook Web
                                </a>
                            @endif
                        @else
                            <span class="text-muted">Web link not available</span>
                        @endif
                    </div>
                </div>
            @endforeach
        @endif
    @elseif(isset($rawApiResponse) && !$rawApiResponse['success'])
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Microsoft Graph API Error</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    {{ $rawApiResponse['error'] ?? 'Unknown error occurred when fetching API data' }}
                </div>
            </div>
        </div>
    @endif

    @if(count($threadMessages) > 0)
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Thread Messages ({{ count($threadMessages) }})</h5>
                @if(auth()->user()->hasRole('master-adminx') && isset($searchMethod))
                    <small class="text-muted">Search method: {{ $searchMethod }}</small>
                @endif
            </div>
            <div class="card-body">
                @foreach($threadMessages as $message)
                    <div class="email-thread-message mb-4 border-bottom pb-3">
                        <div class="row mb-2">
                            <div class="col-md-6">
                                <strong>From:</strong> {{ $message['sender']['emailAddress']['name'] ?? '' }} ({{ $message['sender']['emailAddress']['address'] ?? '' }})
                            </div>
                            <div class="col-md-6">
                                <strong>Received:</strong> {{ \Carbon\Carbon::parse($message['receivedDateTime'])->format('Y-m-d H:i:s') }}
                                @if(isset($message['folderName']))
                                    <span class="badge bg-info">{{ $message['folderName'] }}</span>
                                @endif
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-12">
                                <strong>To:</strong> 
                                @if(isset($message['toRecipients']) && is_array($message['toRecipients']))
                                    @foreach($message['toRecipients'] as $recipient)
                                        {{ $recipient['emailAddress']['address'] ?? '' }}{{ !$loop->last ? ', ' : '' }}
                                    @endforeach
                                @else
                                    N/A
                                @endif
                            </div>
                        </div>
                        @if(isset($message['ccRecipients']) && is_array($message['ccRecipients']) && count($message['ccRecipients']) > 0)
                            <div class="row mb-2">
                                <div class="col-md-12">
                                    <strong>CC:</strong> 
                                    @foreach($message['ccRecipients'] as $recipient)
                                        {{ $recipient['emailAddress']['address'] ?? '' }}{{ !$loop->last ? ', ' : '' }}
                                    @endforeach
                                </div>
                            </div>
                        @endif
                        <div class="row mb-2">
                            <div class="col-md-12">
                                <strong>Subject:</strong> {{ $message['subject'] ?? 'No Subject' }}
                            </div>
                        </div>
                        @if(auth()->user()->hasRole('master-adminx'))
                            <div class="row mb-2">
                                <div class="col-md-12">
                                    <strong>Message ID:</strong> <small>{{ $message['internetMessageId'] ?? 'N/A' }}</small>
                                </div>
                            </div>
                        @endif
                        <div class="row">
                            <div class="col-md-12">
                                <strong>Content:</strong>
                                <div class="border p-3 mt-2">
                                    {!! $message['body']['content'] ?? 'No content' !!}
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @else
        <div class="alert alert-info">
            @if(auth()->user()->hasRole('master-adminx'))
                <p>No thread messages found. This could be because:</p>
                <ul>
                    <li>There are no replies to this email yet</li>
                    <li>The messages might be in a different folder that we couldn't access</li>
                    <li>The internetMessageId might not be properly tracked</li>
                </ul>

                <div class="mt-3">
                    <h6>Debugging Information:</h6>
                    <p><strong>Internet Message ID:</strong> {{ $email->internet_message_id }}</p>
                    <p><strong>Conversation ID:</strong> {{ $email->conversation_id }}</p>
                    <p><strong>Search Method:</strong> {{ $searchMethod ?? 'None' }}</p>
                    @if(isset($error))
                        <p><strong>Error:</strong> {{ $error }}</p>
                    @endif
                </div>
            @endif
        </div>
    @endif
</div>
@endsection
