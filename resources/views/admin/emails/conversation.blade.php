@extends('layouts/layoutSavAdminMaster')

@section('title', 'Email Conversation')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
    <h4 class="fw-bold py-3 mb-4">
        <span class="text-muted fw-light">Email Management / <a href="{{ route('admin.outgoing-emails.index') }}">Outgoing Emails</a> /</span> Conversation
    </h4>

    @if(isset($error))
        <div class="alert alert-danger">
            <h5>Error</h5>
            <p>{{ $error }}</p>
            <a href="{{ route('admin.oauth.microsoft') }}" class="btn btn-primary">Connect to Microsoft</a>
        </div>
    @endif

    <div class="card mb-4">
        <div class="card-header">
            @if(isset($conversationId))
                <h5 class="card-title">Conversation ID: {{ $conversationId }}</h5>
                <div class="mt-2">
                    <a href="{{ route('admin.outgoing-emails.index', ['conversation_id' => $conversationId]) }}" class="btn btn-outline-secondary btn-sm">
                        <i class="ti ti-list me-1"></i> View All Emails with this Conversation ID
                    </a>
                </div>
            @elseif(isset($actionId) && isset($actionName))
                <h5 class="card-title">Action: {{ $actionName }}</h5>
                <div class="mt-2">
                    <a href="{{ route('admin.outgoing-emails.index', ['action_id' => $actionId]) }}" class="btn btn-outline-secondary btn-sm">
                        <i class="ti ti-list me-1"></i> View All Emails with this Action
                    </a>
                </div>
            @endif
        </div>
    </div>

    @if(isset($emails) && count($emails) > 0)
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Local Emails ({{ count($emails) }})</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Action name</th>
                                <th>Subject</th>
                                <th>Sent</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($emails as $email)
                                <tr>
                                    <td>{{ $email->action->name ?? 'N/A' }}</td>
                                    <td>{{ $email->subject }}</td>
                                    <td>{{ $email->sent_at ? $email->sent_at->format('Y-m-d H:i:s') : 'Not sent' }}</td>
                                    <td>
                                        @if($email->status == 'sent')
                                            <span class="badge bg-success">Sent</span>
                                        @elseif($email->status == 'failed')
                                            <span class="badge bg-danger">Failed</span>
                                        @else
                                            <span class="badge bg-warning">{{ ucfirst($email->status) }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.outgoing-emails.thread', $email->id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="ti ti-messages me-1"></i> View Thread
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @else
        <div class="alert alert-info mb-4">
            @if(isset($conversationId))
                <p>No local emails found with this conversation ID.</p>
            @elseif(isset($actionId))
                <p>No local emails found for this action.</p>
            @else
                <p>No local emails found.</p>
            @endif
        </div>
    @endif

    @if(isset($threadMessages) && count($threadMessages) > 0)
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Thread Messages from Microsoft ({{ count($threadMessages) }})</h5>
                @if(isset($searchMethod))
                    <small class="text-muted">Search method: {{ $searchMethod }}</small>
                @endif
            </div>
            <div class="card-body">
                @foreach($threadMessages as $message)
                    <div class="email-thread-message mb-4 border-bottom pb-3">
                        <div class="row mb-2">
                            <div class="col-md-6">
                                <strong>From:</strong> {{ $message['sender']['emailAddress']['name'] ?? '' }} ({{ $message['sender']['emailAddress']['address'] ?? '' }})
                            </div>
                            <div class="col-md-6">
                                <strong>Received:</strong> {{ \Carbon\Carbon::parse($message['receivedDateTime'])->format('Y-m-d H:i:s') }}
                                @if(isset($message['folderName']))
                                    <span class="badge bg-info">{{ $message['folderName'] }}</span>
                                @endif
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-12">
                                <strong>To:</strong> 
                                @if(isset($message['toRecipients']) && is_array($message['toRecipients']))
                                    @foreach($message['toRecipients'] as $recipient)
                                        {{ $recipient['emailAddress']['address'] ?? '' }}{{ !$loop->last ? ', ' : '' }}
                                    @endforeach
                                @else
                                    N/A
                                @endif
                            </div>
                        </div>
                        @if(isset($message['ccRecipients']) && is_array($message['ccRecipients']) && count($message['ccRecipients']) > 0)
                            <div class="row mb-2">
                                <div class="col-md-12">
                                    <strong>CC:</strong> 
                                    @foreach($message['ccRecipients'] as $recipient)
                                        {{ $recipient['emailAddress']['address'] ?? '' }}{{ !$loop->last ? ', ' : '' }}
                                    @endforeach
                                </div>
                            </div>
                        @endif
                        <div class="row mb-2">
                            <div class="col-md-12">
                                <strong>Subject:</strong> {{ $message['subject'] ?? 'No Subject' }}
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-12">
                                <strong>Message ID:</strong> <small>{{ $message['internetMessageId'] ?? 'N/A' }}</small>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <strong>Content:</strong>
                                <div class="border p-3 mt-2">
                                    {!! $message['body']['content'] ?? 'No content' !!}
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @else
        <div class="alert alert-info">
            @if(isset($actionId))
                <p>No Microsoft thread messages are shown when filtering by Action.</p>
            @else
                <p>No thread messages found from Microsoft. This could be because:</p>
                <ul>
                    <li>There are no emails with this conversation ID in your Microsoft account</li>
                    <li>The messages might be in a different folder that we couldn't access</li>
                    <li>The conversation ID might not be properly tracked</li>
                </ul>

                <div class="mt-3">
                    <h6>Debugging Information:</h6>
                    <p><strong>Conversation ID:</strong> {{ $conversationId }}</p>
                    <p><strong>Search Method:</strong> {{ $searchMethod ?? 'None' }}</p>
                    @if(isset($error))
                        <p><strong>Error:</strong> {{ $error }}</p>
                    @endif
                </div>
            @endif
        </div>
    @endif
</div>
@endsection
