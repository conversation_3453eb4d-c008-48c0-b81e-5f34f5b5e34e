@extends('layouts/layoutSavAdminMaster')

@section('title', 'View Action')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
    <h4 class="fw-bold py-3 mb-4">
        <span class="text-muted fw-light">Actions Management /</span> View Action
    </h4>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title">Action Details</h5>
                    <div>
                        <a href="{{ route('admin.actions.edit', $action->id) }}" class="btn btn-primary">
                            <i class="ti ti-pencil me-1"></i> Edit
                        </a>
                        <a href="{{ route('admin.actions.index') }}" class="btn btn-secondary">
                            <i class="ti ti-arrow-left me-1"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-semibold">Name:</h6>
                            <p>{{ $action->name }}</p>
                        </div>
                        <div class="col-md-3 mb-3">
                            <h6 class="fw-semibold">Start Date:</h6>
                            <p>{{ $action->start_date->format('Y-m-d') }}</p>
                        </div>
                        <div class="col-md-3 mb-3">
                            <h6 class="fw-semibold">End Date:</h6>
                            <p>{{ $action->end_date->format('Y-m-d') }}</p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <h6 class="fw-semibold">Departure City:</h6>
                            <p>{{ $action->oras_plecare }}</p>
                        </div>
                        <div class="col-md-4 mb-3">
                            <h6 class="fw-semibold">Destination City:</h6>
                            <p>{{ $action->destinatie_city }}</p>
                        </div>
                        <div class="col-md-4 mb-3">
                            <h6 class="fw-semibold">Destination Country:</h6>
                            <p>{{ $action->destinatie_tara }}</p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <h6 class="fw-semibold">Destination Latitude:</h6>
                            <p>{{ $action->destinatei_lat }}</p>
                        </div>
                        <div class="col-md-3 mb-3">
                            <h6 class="fw-semibold">Destination Longitude:</h6>
                            <p>{{ $action->destinatie_long }}</p>
                        </div>
                        <div class="col-md-3 mb-3">
                            <h6 class="fw-semibold">Max Participants:</h6>
                            <p>{{ $action->max_participanti }}</p>
                        </div>
                        <div class="col-md-3 mb-3">
                            <h6 class="fw-semibold">Completion Date:</h6>
                            <p>{{ $action->data_finalizare_actiune ? $action->data_finalizare_actiune->format('Y-m-d') : 'Not set' }}</p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-semibold">Status:</h6>
                            <p>
                                @if($action->status === 'activ')
                                    <span class="badge bg-success">Active</span>
                                @elseif($action->status === 'finalizat')
                                    <span class="badge bg-info">Finalized</span>
                                @elseif($action->status === 'pending')
                                    <span class="badge bg-warning">Pending</span>
                                @elseif($action->status === 'blocat')
                                    <span class="badge bg-danger">Blocked</span>
                                @endif
                            </p>
                        </div>
                        <div class="col-md-3 mb-3">
                            <h6 class="fw-semibold">Created At:</h6>
                            <p>{{ $action->created_at->format('Y-m-d H:i:s') }}</p>
                        </div>
                        <div class="col-md-3 mb-3">
                            <h6 class="fw-semibold">Updated At:</h6>
                            <p>{{ $action->updated_at->format('Y-m-d H:i:s') }}</p>
                        </div>
                    </div>

                    <div class="mt-4">
                        <form action="{{ route('admin.actions.destroy', $action->id) }}" method="POST" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this action?')">
                                <i class="ti ti-trash me-1"></i> Delete Action
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection