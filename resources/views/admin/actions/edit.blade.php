@extends('layouts/layoutSavAdminMaster')

@section('title', 'Edit Action')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
    <h4 class="fw-bold py-3 mb-4">
        <span class="text-muted fw-light">Actions Management /</span> Edit Action
    </h4>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Edit Action</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.actions.update', $action->id) }}">
                        @csrf
                        @method('PUT')
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">Name</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" name="name" value="{{ old('name', $action->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-3">
                                <label class="form-label">Start Date</label>
                                <input type="date" class="form-control @error('start_date') is-invalid @enderror" name="start_date" value="{{ old('start_date', $action->start_date->format('Y-m-d')) }}" required>
                                @error('start_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-3">
                                <label class="form-label">End Date</label>
                                <input type="date" class="form-control @error('end_date') is-invalid @enderror" name="end_date" value="{{ old('end_date', $action->end_date->format('Y-m-d')) }}" required>
                                @error('end_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-4">
                                <label class="form-label">Departure City</label>
                                <input type="text" class="form-control @error('oras_plecare') is-invalid @enderror" name="oras_plecare" value="{{ old('oras_plecare', $action->oras_plecare) }}" required>
                                @error('oras_plecare')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-4">
                                <label class="form-label">Destination City</label>
                                <input type="text" class="form-control @error('destinatie_city') is-invalid @enderror" name="destinatie_city" value="{{ old('destinatie_city', $action->destinatie_city) }}" required>
                                @error('destinatie_city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-4">
                                <label class="form-label">Destination Country</label>
                                <input type="text" class="form-control @error('destinatie_tara') is-invalid @enderror" name="destinatie_tara" value="{{ old('destinatie_tara', $action->destinatie_tara) }}" required>
                                @error('destinatie_tara')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-3">
                                <label class="form-label">Destination Latitude</label>
                                <input type="text" class="form-control @error('destinatei_lat') is-invalid @enderror" name="destinatei_lat" value="{{ old('destinatei_lat', $action->destinatei_lat) }}" required>
                                @error('destinatei_lat')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-3">
                                <label class="form-label">Destination Longitude</label>
                                <input type="text" class="form-control @error('destinatie_long') is-invalid @enderror" name="destinatie_long" value="{{ old('destinatie_long', $action->destinatie_long) }}" required>
                                @error('destinatie_long')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-3">
                                <label class="form-label">Max Participants</label>
                                <input type="number" class="form-control @error('max_participanti') is-invalid @enderror" name="max_participanti" value="{{ old('max_participanti', $action->max_participanti) }}" required min="1">
                                @error('max_participanti')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-3">
                                <label class="form-label">Completion Date</label>
                                <input type="date" class="form-control @error('data_finalizare_actiune') is-invalid @enderror" name="data_finalizare_actiune" value="{{ old('data_finalizare_actiune', $action->data_finalizare_actiune ? $action->data_finalizare_actiune->format('Y-m-d') : '') }}">
                                @error('data_finalizare_actiune')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">Status</label>
                                <select class="form-select @error('status') is-invalid @enderror" name="status" required>
                                    <option value="activ" {{ old('status', $action->status) == 'activ' ? 'selected' : '' }}>Active</option>
                                    <option value="finalizat" {{ old('status', $action->status) == 'finalizat' ? 'selected' : '' }}>Finalized</option>
                                    <option value="pending" {{ old('status', $action->status) == 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="blocat" {{ old('status', $action->status) == 'blocat' ? 'selected' : '' }}>Blocked</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-12 mt-4">
                                <button type="submit" class="btn btn-primary">Update Action</button>
                                <a href="{{ route('admin.actions.index') }}" class="btn btn-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection