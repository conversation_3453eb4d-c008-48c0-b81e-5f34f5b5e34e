@extends('layouts/layoutSavAdminMaster')

@section('title', 'Actions')

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
    <h4 class="fw-bold py-3 mb-4">
        <span class="text-muted fw-light">Actions Management /</span> Actions
    </h4>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Filter Actions</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.actions.index') }}">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">Name</label>
                                <input type="text" class="form-control" name="name" value="{{ request('name') }}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Status</label>
                                <select class="form-select" name="status">
                                    <option value="">All Statuses</option>
                                    <option value="activ" {{ request('status') == 'activ' ? 'selected' : '' }}>Active</option>
                                    <option value="finalizat" {{ request('status') == 'finalizat' ? 'selected' : '' }}>Finalized</option>
                                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="blocat" {{ request('status') == 'blocat' ? 'selected' : '' }}>Blocked</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="{{ route('admin.actions.index') }}" class="btn btn-secondary">Reset</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Actions</h5>
                </div>
                <div class="card-body">
                    <a href="{{ route('admin.actions.create') }}" class="btn btn-primary">
                        <i class="ti ti-plus me-1"></i> Create New Action
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-title">Actions List</h5>
        </div>
        <div class="table-responsive text-nowrap">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Start Date</th>
                        <th>End Date</th>
                        <th>Departure City</th>
                        <th>Destination</th>
                        <th>Max Participants</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody class="table-border-bottom-0">
                    @forelse($actions as $action)
                    <tr>
                        <td>{{ $action->id }}</td>
                        <td>{{ $action->name }}</td>
                        <td>{{ $action->start_date->format('Y-m-d') }}</td>
                        <td>{{ $action->end_date->format('Y-m-d') }}</td>
                        <td>{{ $action->oras_plecare }}</td>
                        <td>{{ $action->destinatie_city }}, {{ $action->destinatie_tara }}</td>
                        <td>{{ $action->max_participanti }}</td>
                        <td>
                            @if($action->status === 'activ')
                                <span class="badge bg-success">Active</span>
                            @elseif($action->status === 'finalizat')
                                <span class="badge bg-info">Finalized</span>
                            @elseif($action->status === 'pending')
                                <span class="badge bg-warning">Pending</span>
                            @elseif($action->status === 'blocat')
                                <span class="badge bg-danger">Blocked</span>
                            @endif
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ route('admin.actions.show', $action->id) }}" class="btn btn-sm btn-info">
                                    <i class="ti ti-eye me-1"></i> View
                                </a>
                                <a href="{{ route('admin.actions.edit', $action->id) }}" class="btn btn-sm btn-primary">
                                    <i class="ti ti-pencil me-1"></i> Edit
                                </a>
                                <form action="{{ route('admin.actions.destroy', $action->id) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this action?')">
                                        <i class="ti ti-trash me-1"></i> Delete
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="9" class="text-center">No actions found</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="card-footer">
            {{ $actions->links() }}
        </div>
    </div>
</div>
@endsection