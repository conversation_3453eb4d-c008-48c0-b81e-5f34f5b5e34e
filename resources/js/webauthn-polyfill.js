// WebAuthn Polyfill
// This script provides a basic polyfill for the CredentialsContainer API
// to prevent errors when the browser doesn't support it or when it's blocked by CORS

// Wrap in a function that checks for document readiness to ensure jQuery is available
(function() {
  'use strict';

  // Function to initialize the polyfill
  function initPolyfill() {
    // Check if the CredentialsContainer is already defined
    if (typeof window.CredentialsContainer === 'undefined') {
      console.warn('WebAuthn API (CredentialsContainer) is not available. Using polyfill.');

      // Create a basic implementation of CredentialsContainer
      window.CredentialsContainer = function() {};

      // Add stub methods for common CredentialsContainer methods
      window.CredentialsContainer.prototype.create = function() {
        return Promise.reject(new Error('WebAuthn is not supported in this browser or environment.'));
      };

      window.CredentialsContainer.prototype.get = function() {
        return Promise.reject(new Error('WebAuthn is not supported in this browser or environment.'));
      };

      window.CredentialsContainer.prototype.store = function() {
        return Promise.reject(new Error('WebAuthn is not supported in this browser or environment.'));
      };

      // Assign the polyfill to navigator.credentials if it doesn't exist
      if (typeof navigator.credentials === 'undefined') {
        navigator.credentials = new window.CredentialsContainer();
      }
    }
  }

  // Check if the document is already loaded
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
    // Document is already loaded, initialize immediately
    initPolyfill();
  } else {
    // Wait for the document to be ready
    document.addEventListener('DOMContentLoaded', initPolyfill);
  }
})();
