// Email Wizard JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Variables to store email template data
    let currentTemplate = null;
    let customVariables = {};
    let commonVariablesData = {};
    let isHtmlMode = true; // Always use HTML mode (HTML Source option removed)
    window.htmlContent = ''; // Store HTML content (global for access from other scripts)
    let textContent = ''; // Store plain text content
    let quill = null; // Quill editor instance
    let hasValidToken = false; // Flag to track if user has a valid Microsoft OAuth token

    // Initialize Quill editor if available
    if (typeof Quill !== 'undefined') {
        try {
            quill = new Quill('#quill-editor', {
                modules: {
                    toolbar: [
                        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'list': 'ordered' }, { 'list': 'bullet' }],
                        [{ 'align': [] }],
                        ['link'],
                        ['clean']
                    ]
                },
                placeholder: 'Compose your email content...',
                theme: 'snow'
            });

            // Set up Quill change event to update htmlContent
            quill.on('text-change', function() {
                window.htmlContent = quill.root.innerHTML;
                updatePreview();
            });

            console.log('Quill editor initialized successfully');
        } catch (e) {
            console.error('Error initializing Quill:', e);
            // Show the fallback textarea if Quill initialization fails
            document.getElementById('emailContent').style.display = 'block';
            document.getElementById('quill-editor').style.display = 'none';
        }
    } else {
        console.error('Quill is not loaded. Using fallback textarea.');
        document.getElementById('emailContent').style.display = 'block';
        document.getElementById('quill-editor').style.display = 'none';
    }

    // Initialize wizard navigation
    document.querySelectorAll('.next-step').forEach(button => {
        button.addEventListener('click', function() {
            const nextStep = this.getAttribute('data-next');
            if (!nextStep) return;

            // Check if an email template is selected when trying to navigate from step 1
            if (this.closest('#step1') && nextStep !== 'step1') {
                const templateElement = document.getElementById('emailTemplate');
                if (!templateElement) return;

                const templateId = templateElement.value;
                if (!templateId) {
                    Swal.fire({
                        title: 'Template Required',
                        text: 'Please select an email template before proceeding.',
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        showCancelButton: false,
                        showDenyButton: false,
                        buttonsStyling: false,
                        customClass: {
                            confirmButton: 'btn btn-primary'
                        }
                    });
                    return;
                }
            }

            const nextTabElement = document.querySelector('#' + nextStep + '-tab');
            if (nextTabElement) {
                nextTabElement.click();
            }
        });
    });

    document.querySelectorAll('.prev-step').forEach(button => {
        button.addEventListener('click', function() {
            const prevStep = this.getAttribute('data-prev');
            if (!prevStep) return;

            const prevTabElement = document.querySelector('#' + prevStep + '-tab');
            if (prevTabElement) {
                prevTabElement.click();
            }
        });
    });

    // HTML Source option removed, always use HTML mode
    // Keep this code for compatibility, but ensure isHtmlMode is always true
    document.querySelectorAll('input[name="editorMode"]').forEach(radio => {
        radio.addEventListener('change', function() {
            isHtmlMode = true; // Always use HTML mode
            // No need to call updateEditorContent() as we're always in HTML mode
        });
    });

    // Function to convert HTML to plain text
    function htmlToText(html) {
        // Create a temporary div to hold the HTML
        const temp = document.createElement('div');
        temp.innerHTML = html;

        // Replace common HTML elements with text equivalents
        const content = temp.textContent || temp.innerText || '';

        // Basic formatting for readability
        return content.replace(/\s+/g, ' ').trim();
    }

    // Function to update the editor content (HTML Source option removed, always use HTML mode)
    function updateEditorContent() {
        const contentField = document.getElementById('emailContent');
        const quillEditor = document.getElementById('quill-editor');
        const quillToolbar = document.querySelector('.ql-toolbar');
        const quillContainer = document.querySelector('.ql-container');

        // Always use HTML mode - show Quill and toolbar, hide textarea
        if (quillToolbar) {
            quillToolbar.style.display = 'block';
            quillToolbar.style.visibility = 'visible';
        }
        if (quillContainer) {
            quillContainer.style.display = 'block';
            quillContainer.style.visibility = 'visible';
        }
        quillEditor.style.display = 'block';
        quillEditor.style.visibility = 'visible';
        contentField.style.display = 'none';

        // Update Quill content if it exists
        if (quill) {
            // Prevent the text-change event from firing during this update
            const oldHtml = quill.root.innerHTML;
            if (oldHtml !== htmlContent) {
                quill.root.innerHTML = htmlContent;
            }
        }

        // Update preview
        updatePreview();
    }

    // Handle email template selection
    document.getElementById('emailTemplate').addEventListener('change', function() {
        const templateId = this.value;
        if (!templateId) {
            currentTemplate = null;
            document.getElementById('customVariablesContainer').innerHTML = 
                '<div class="alert alert-info">' +
                'Please select an email template first to see custom variables.' +
                '</div>';
            document.getElementById('emailContent').value = '';
            window.htmlContent = '';
            textContent = '';
            return;
        }

        // Fetch template data
        fetch('/admin/config/email-templates/' + templateId + '/json')
            .then(response => response.json())
            .then(data => {
                currentTemplate = data;

                // Update email content based on current mode
                window.htmlContent = data.content;
                textContent = htmlToText(data.content);

                // Set the content in the appropriate editor based on current mode
                if (isHtmlMode && quill) {
                    quill.root.innerHTML = window.htmlContent;
                } else {
                    document.getElementById('emailContent').value = isHtmlMode ? window.htmlContent : textContent;
                }

                // Make sure the correct editor is displayed
                updateEditorContent();

                // Update email subject with the template's subject
                document.getElementById('emailSubject').value = data.subject;

                // Generate custom variable inputs
                generateCustomVariableInputs(data.variables);

                // Update preview
                updatePreview();
            })
            .catch(error => {
                console.error('Error fetching template:', error);
                Swal.fire({
                    title: 'Error',
                    text: 'Failed to load email template. Please try again.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            });
    });

    // Generate custom variable inputs
    function generateCustomVariableInputs(variables) {
        const container = document.getElementById('customVariablesContainer');

        if (!variables || variables.length === 0) {
            container.innerHTML = 
                '<div class="alert alert-info">' +
                'This template has no custom variables.' +
                '</div>';
            return;
        }

        let html = '';
        variables.forEach(variable => {
            const varName = variable.name || variable;
            const varId = 'var_' + varName.replace(/[^a-zA-Z0-9]/g, '_');

            html += 
                '<div class="mb-3">' +
                '<label for="' + varId + '" class="form-label">' + varName + '</label>' +
                '<input type="text" class="form-control custom-variable-input" ' +
                'id="' + varId + '" data-var-name="' + varName + '" ' +
                'value="' + (customVariables[varName] || '') + '">' +
                '</div>';
        });

        container.innerHTML = html;

        // Add event listeners to the new inputs
        document.querySelectorAll('.custom-variable-input').forEach(input => {
            input.addEventListener('input', function() {
                const varName = this.getAttribute('data-var-name');
                if (!varName) return;

                customVariables[varName] = this.value;
                updatePreview();
            });
        });
    }


    // HTML Source option removed, always use HTML mode
    // Keep this code for compatibility, but it won't be used
    document.getElementById('emailContent').addEventListener('input', function() {
        // No need to update htmlContent as we're always in HTML mode
        updatePreview();
    });

    // Handle email subject changes
    document.getElementById('emailSubject').addEventListener('input', function() {
        updatePreview();
    });

    // Handle confirmation checkbox - handled by the updated event listener below

    // Update preview (HTML Source option removed, always use HTML mode)
    function updatePreview() {
        const subjectElement = document.getElementById('emailSubject');
        if (!subjectElement) return;

        const subject = subjectElement.value;

        // Always get the latest content from the Quill editor
        if (quill) {
            window.htmlContent = quill.root.innerHTML;
        }

        // For preview, we always use HTML content
        let previewContent = window.htmlContent;

        // Prepare variables for preview
        const allVariables = Object.assign({}, customVariables, commonVariablesData);

        // Update subject preview
        let previewSubject = subject;
        for (const key in allVariables) {
            if (allVariables.hasOwnProperty(key)) {
                const value = allVariables[key];
                const regex = new RegExp('{{' + key + '}}', 'g');
                previewSubject = previewSubject.replace(regex, value || '[' + key + ']');
            }
        }

        const previewSubjectElement = document.getElementById('previewSubject');
        if (previewSubjectElement) {
            previewSubjectElement.textContent = previewSubject;
        }

        // Update content preview
        for (const key in allVariables) {
            if (allVariables.hasOwnProperty(key)) {
                const value = allVariables[key];
                const regex = new RegExp('{{' + key + '}}', 'g');
                previewContent = previewContent.replace(regex, value || '[' + key + ']');
            }
        }

        const previewContentElement = document.getElementById('previewContent');
        if (previewContentElement) {
            previewContentElement.innerHTML = previewContent;
        }
    }

    // Extract variables from template content
    function extractVariablesFromTemplate(content) {
        const regex = /{{([^}]+)}}/g;
        const variables = new Set();
        let match;

        while ((match = regex.exec(content)) !== null) {
            variables.add(match[1]);
        }

        return Array.from(variables);
    }

    // Load common variables data for the first hotel (for preview purposes)
    function loadCommonVariablesData() {
        const hotelIdElement = document.getElementById('firstHotelId');
        if (!hotelIdElement) return;

        const hotelId = hotelIdElement.value;
        if (!hotelId) return;

        // Get the template content (HTML Source option removed, always use HTML mode)
        let templateContent = '';
        if (quill) {
            templateContent = quill.root.innerHTML;
        } else {
            templateContent = window.htmlContent;
        }

        // Get the subject
        const subjectElement = document.getElementById('emailSubject');
        if (!subjectElement) return;

        const subject = subjectElement.value;

        // Extract variables from template content and subject
        const contentVariables = extractVariablesFromTemplate(templateContent);
        const subjectVariables = extractVariablesFromTemplate(subject);
        const allTemplateVariables = [...new Set([...contentVariables, ...subjectVariables])];

        // If no variables found, no need to fetch data
        if (allTemplateVariables.length === 0) {
            commonVariablesData = {};
            updatePreview();
            return;
        }

        fetch('/admin/hotels/' + hotelId + '/variables')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (!data) {
                    console.warn('No data returned from variables API');
                    return;
                }

                // Filter the data to only include variables that are present in the template
                const filteredData = {};
                for (const key in data) {
                    if (data.hasOwnProperty(key) && allTemplateVariables.includes(key)) {
                        filteredData[key] = data[key];
                    }
                }
                commonVariablesData = filteredData;
                updatePreview();
            })
            .catch(error => {
                console.error('Error loading common variables:', error);
            });
    }

    // Send Email button
    const sendEmailBtn = document.getElementById('sendEmailBtn');
    if (sendEmailBtn) {
        sendEmailBtn.addEventListener('click', function() {
            const subjectElement = document.getElementById('emailSubject');
            if (!subjectElement) {
                console.error('Email subject element not found');
                return;
            }
            const subject = subjectElement.value;

            const templateElement = document.getElementById('emailTemplate');
            if (!templateElement) {
                console.error('Email template element not found');
                return;
            }
            const templateId = templateElement.value;

            // Always use HTML content for sending (HTML Source option removed, always use HTML mode)
            // Make sure we have the latest content from the Quill editor
            if (quill) {
                window.htmlContent = quill.root.innerHTML;
            }

            const content = window.htmlContent;

            if (!subject) {
                Swal.fire({
                    title: 'Error',
                    text: 'Please enter an email subject',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }

            if (!templateId) {
                Swal.fire({
                    title: 'Error',
                    text: 'Please select an email template',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }

            // Get the CSRF token
            const csrfTokenElement = document.querySelector('meta[name="csrf-token"]');
            if (!csrfTokenElement) {
                console.error('CSRF token element not found');
                Swal.fire({
                    title: 'Error',
                    text: 'CSRF token not found. Please refresh the page and try again.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }
            const csrfToken = csrfTokenElement.getAttribute('content');

            // Get the hotel IDs
            const hotelIdsElement = document.getElementById('hotelIds');
            if (!hotelIdsElement) {
                Swal.fire({
                    title: 'Error',
                    text: 'Hotel IDs not found',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }

            const hotelIds = JSON.parse(hotelIdsElement.value);

            // Get the action_id if available
            const actionSelect = document.getElementById('actionSelect');
            const actionId = actionSelect && actionSelect.style.display !== 'none' ? actionSelect.value : null;

            // Prepare data for sending
            const data = {
                subject: subject,
                template_id: templateId,
                content: content,
                custom_variables: customVariables,
                hotel_ids: hotelIds,
                action_id: actionId
            };

            // Show loading indicator
            Swal.fire({
                title: 'Sending Emails',
                text: 'Please wait while we send the emails...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Send the request
            fetch('/admin/hotels/send-emails', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        // Check if OAuth is required
                        if (data.oauth_required && data.oauth_url) {
                            Swal.fire({
                                title: 'Autentificare Microsoft necesară',
                                html: data.message || 'Pentru a trimite email-uri prin Microsoft, trebuie să vă autentificați cu contul Microsoft.',
                                icon: 'warning',
                                showCancelButton: true,
                                confirmButtonText: 'Autentificare',
                                cancelButtonText: 'Anulare'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    // Store current form state in localStorage
                                    storeFormState();

                                    // Get the URL from the "Back to Map" button if it exists
                                    const backToMapBtn = document.querySelector('a.btn.btn-secondary[href*="admin/hotels/map"]');
                                    let returnUrl;

                                    if (backToMapBtn) {
                                        // Use the "Back to Map" button's href as the return URL
                                        returnUrl = backToMapBtn.href;
                                        console.log('Using "Back to Map" URL as return URL:', returnUrl);
                                    } else {
                                        // Fallback to the current URL with #step4 fragment
                                        const currentUrl = window.location.href.split('#')[0]; // Remove any existing fragment
                                        returnUrl = currentUrl + '#step4';
                                        console.log('No "Back to Map" button found, using current URL with #step4 fragment:', returnUrl);
                                    }

                                    // Construct the OAuth URL with the return URL as a parameter
                                    const oauthUrl = data.oauth_url + 
                                        (data.oauth_url.includes('?') ? '&' : '?') + 
                                        'return_url=' + encodeURIComponent(returnUrl);

                                    console.log('Redirecting to OAuth with return URL:', oauthUrl);

                                    // Redirect to Microsoft OAuth with the return URL
                                    window.location.href = oauthUrl;
                                }
                            });
                        } else {
                            throw new Error(data.message || `HTTP error! status: ${response.status}`);
                        }
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'Success',
                        text: data.message || 'Emails have been sent successfully.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                } else {
                    Swal.fire({
                        title: 'Error',
                        text: data.message || 'Failed to send emails. Please try again.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            })
            .catch(error => {
                console.error('Error sending emails:', error);
                Swal.fire({
                    title: 'Error',
                    text: 'An unexpected error occurred. Please try again.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            });
        });
    }

    // Prevent direct tab navigation if no template is selected
    const tabLinks = document.querySelectorAll('#emailWizardTabs .nav-link');
    if (tabLinks && tabLinks.length > 0) {
        tabLinks.forEach(tab => {
            tab.addEventListener('click', function(event) {
                // Skip validation for step 1
                if (this.id === 'step1-tab') return;

                // Check if an email template is selected
                const templateElement = document.getElementById('emailTemplate');
                if (!templateElement) {
                    console.error('Email template element not found');
                    event.preventDefault();
                    event.stopPropagation();
                    return false;
                }

                const templateId = templateElement.value;
                if (!templateId) {
                    event.preventDefault();
                    event.stopPropagation();

                    Swal.fire({
                        title: 'Template Required',
                        text: 'Please select an email template before proceeding.',
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        showCancelButton: false,
                        showDenyButton: false,
                        buttonsStyling: false,
                        customClass: {
                            confirmButton: 'btn btn-primary'
                        }
                    });

                    // Keep the first tab active
                    const firstTab = document.querySelector('#step1-tab');
                    if (firstTab) {
                        firstTab.click();
                    }
                    return false;
                }
            });
        });
    }

    // Function to check Microsoft OAuth token status
    function checkMicrosoftOAuthTokenStatus() {
        fetch('/admin/oauth/microsoft/status/check')
            .then(response => response.json())
            .then(data => {
                // Get all token status containers
                const tokenStatusContainer = document.getElementById('microsoftTokenStatus');
                const tokenStatusContainer1 = document.getElementById('microsoftTokenStatus1');
                const tokenStatusContainer2 = document.getElementById('microsoftTokenStatus2');
                const tokenStatusContainer3 = document.getElementById('microsoftTokenStatus3');
                const sendEmailBtn = document.getElementById('sendEmailBtn');

                // Check if at least one container exists
                if (!tokenStatusContainer && !tokenStatusContainer1 && !tokenStatusContainer2 && !tokenStatusContainer3) {
                    console.error('No token status containers found');
                    return;
                }

                if (data.status === 'success' && data.token_exists) {
                    // Token exists and is valid
                    hasValidToken = true;

                    // Display success message only in step 4
                    if (tokenStatusContainer) {
                        tokenStatusContainer.innerHTML = `
                            <div class="alert alert-success mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="ti ti-check-circle me-2"></i>
                                    <span>Microsoft OAuth token is valid. You can send emails through Microsoft.</span>
                                </div>
                            </div>
                        `;
                    }

                    // Clear any warning messages in steps 1-3
                    [tokenStatusContainer1, tokenStatusContainer2, tokenStatusContainer3].forEach(container => {
                        if (container) {
                            container.innerHTML = '';
                        }
                    });

                    // Enable the send button if the confirmation checkbox is checked
                    const confirmCheckbox = document.getElementById('confirmSend');
                    if (confirmCheckbox && confirmCheckbox.checked) {
                        sendEmailBtn.disabled = false;
                    }
                } else {
                    // Token doesn't exist or is invalid
                    hasValidToken = false;

                    // Create the warning message HTML
                    const warningHtml = `
                        <div class="alert alert-warning mb-3">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-alert-triangle me-2"></i>
                                <span>Nu puteți trimite emailuri prin Outlook deoarece nu aveți un token Microsoft valid.</span>
                            </div>
                            <div class="mt-2">
                                <button type="button" class="microsoftAuthBtn btn btn-sm btn-primary">
                                    <i class="ti ti-brand-microsoft me-1"></i> Autentificare Microsoft
                                </button>
                            </div>
                        </div>
                    `;

                    // Display warning message in all steps
                    [tokenStatusContainer, tokenStatusContainer1, tokenStatusContainer2, tokenStatusContainer3].forEach(container => {
                        if (container) {
                            container.innerHTML = warningHtml;
                        }
                    });

                    // Disable the send button
                    if (sendEmailBtn) {
                        sendEmailBtn.disabled = true;
                    }

                    // Add event listener to all Microsoft auth buttons
                    document.querySelectorAll('.microsoftAuthBtn').forEach(btn => {
                        btn.addEventListener('click', function() {
                            // Store current form state in localStorage
                            storeFormState();

                            // Get the URL from the "Back to Map" button if it exists
                            const backToMapBtn = document.querySelector('a.btn.btn-secondary[href*="admin/hotels/map"]');
                            let returnUrl;

                            if (backToMapBtn) {
                                // Use the "Back to Map" button's href as the return URL
                                returnUrl = backToMapBtn.href;
                                console.log('Using "Back to Map" URL as return URL:', returnUrl);
                            } else {
                                // Fallback to the current URL with #step4 fragment
                                const currentUrl = window.location.href.split('#')[0]; // Remove any existing fragment
                                returnUrl = currentUrl + '#step4';
                                console.log('No "Back to Map" button found, using current URL with #step4 fragment:', returnUrl);
                            }

                            // Construct the OAuth URL with the return URL as a parameter
                            const oauthUrl = (data.oauth_url || '/admin/oauth/microsoft') + 
                                '?return_url=' + encodeURIComponent(returnUrl);

                            console.log('Redirecting to OAuth with return URL:', oauthUrl);

                            // Redirect to Microsoft OAuth with the return URL
                            window.location.href = oauthUrl;
                        });
                    });
                }
            })
            .catch(error => {
                console.error('Error checking Microsoft OAuth token status:', error);

                // Get all token status containers
                const containers = [
                    document.getElementById('microsoftTokenStatus'),
                    document.getElementById('microsoftTokenStatus1'),
                    document.getElementById('microsoftTokenStatus2'),
                    document.getElementById('microsoftTokenStatus3')
                ];

                // Display error message in all containers
                containers.forEach(container => {
                    if (container) {
                        container.innerHTML = `
                            <div class="alert alert-danger mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="ti ti-alert-circle me-2"></i>
                                    <span>Error checking Microsoft OAuth token status. Please try again.</span>
                                </div>
                            </div>
                        `;
                    }
                });
            });
    }

    // Function to store form state in localStorage
    function storeFormState() {
        const formData = {
            subject: document.getElementById('emailSubject')?.value || '',
            templateId: document.getElementById('emailTemplate')?.value || '',
            customVariables: customVariables,
            htmlContent: window.htmlContent
        };

        localStorage.setItem('emailWizardFormState', JSON.stringify(formData));
    }

    // Function to restore form state from localStorage
    function restoreFormState() {
        const storedFormState = localStorage.getItem('emailWizardFormState');
        if (!storedFormState) return;

        try {
            const formData = JSON.parse(storedFormState);

            // Restore subject
            const subjectElement = document.getElementById('emailSubject');
            if (subjectElement && formData.subject) {
                subjectElement.value = formData.subject;
            }

            // Restore template selection
            const templateElement = document.getElementById('emailTemplate');
            if (templateElement && formData.templateId) {
                templateElement.value = formData.templateId;
                // Trigger change event to load template content
                const event = new Event('change');
                templateElement.dispatchEvent(event);
            }

            // Restore custom variables
            if (formData.customVariables) {
                customVariables = formData.customVariables;
            }

            // Restore HTML content
            if (formData.htmlContent && quill) {
                window.htmlContent = formData.htmlContent;
                quill.root.innerHTML = formData.htmlContent;
            }

            // Clear stored form state
            localStorage.removeItem('emailWizardFormState');
        } catch (error) {
            console.error('Error restoring form state:', error);
        }
    }

    // Update the confirmSend event listener to check for valid token
    document.getElementById('confirmSend')?.addEventListener('change', function() {
        const sendEmailBtn = document.getElementById('sendEmailBtn');
        if (sendEmailBtn) {
            sendEmailBtn.disabled = !(this.checked && hasValidToken);
        }
    });

    // Function to check for hash fragment and activate the corresponding tab
    function checkHashAndActivateTab() {
        const hash = window.location.hash;
        if (hash && hash.startsWith('#step')) {
            const stepTab = document.querySelector(hash + '-tab');
            if (stepTab) {
                console.log('Activating tab based on hash fragment:', hash);

                // If we're trying to go to step 4 directly, we need to make sure a template is selected
                if (hash === '#step4') {
                    const templateElement = document.getElementById('emailTemplate');
                    if (templateElement && templateElement.value) {
                        // Template is selected, we can proceed to step 4
                        setTimeout(() => {
                            stepTab.click();
                        }, 500); // Small delay to ensure the page is fully loaded
                    } else {
                        console.warn('Cannot go to step 4 directly without a template selected');
                    }
                } else {
                    // For other steps, just activate the tab
                    setTimeout(() => {
                        stepTab.click();
                    }, 500); // Small delay to ensure the page is fully loaded
                }
            }
        }
    }

    // Initialize
    loadCommonVariablesData();
    checkMicrosoftOAuthTokenStatus();
    restoreFormState();

    // Check for hash fragment after form state is restored
    checkHashAndActivateTab();
});
