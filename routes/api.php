<?php

use App\Http\Controllers\Api\HotelApiController;
use App\Http\Controllers\Api\Auth\AuthController;
use App\Http\Controllers\Api\Auth\PasswordResetController;
use App\Http\Controllers\Api\Auth\EmailVerificationController;
use Illuminate\Support\Facades\Route;

// Authentication Routes
Route::prefix('auth')->group(function () {
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/forgot-password', [PasswordResetController::class, 'sendResetLinkEmail']);
    Route::post('/reset-password', [PasswordResetController::class, 'reset']);

    // Protected authentication routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/user', [AuthController::class, 'user']);

        // Email verification
        Route::post('/email/verification-notification', [EmailVerificationController::class, 'sendVerificationEmail']);
        Route::get('/email/verify/{id}/{hash}', [EmailVerificationController::class, 'verify'])->name('verification.verify');
        Route::get('/email/verify', [EmailVerificationController::class, 'checkVerification']);
    });
});

// Routes that don't require authentication
Route::get('/markets-by-chain', [HotelApiController::class, 'marketsByChain'])->name('api.hotel-api.markets-by-chain');
Route::get('/brands-by-chain-and-market', [HotelApiController::class, 'brandsByChainAndMarket'])->name('api.hotel-api.brands-by-chain-and-market');
Route::get('/search-cities', [HotelApiController::class, 'searchCities'])->name('api.hotel-api.search-cities');

// Protected API Routes
Route::middleware('auth:sanctum')->group(function () {
	// Routes that don't require email verification
	Route::get('/countries', [HotelApiController::class, 'countries'])->name('api.hotel-api.countries');
	Route::get('/region', [HotelApiController::class, 'region']);
	Route::get('/subregion', [HotelApiController::class, 'subregion']);

	// Routes that require email verification
	Route::middleware([\App\Http\Middleware\EnsureEmailIsVerifiedApi::class])->group(function () {
		Route::get('/hotels-for-map', [HotelApiController::class, 'hotelsForMap'])->name('api.hotel-api.hotels-for-map');
	});
});
