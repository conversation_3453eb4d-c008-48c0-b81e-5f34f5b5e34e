<?php

use App\Http\Controllers\Api\HotelApiController;
use App\Http\Controllers\Api\TeamApiController;
use App\Http\Controllers\Backend\Hotels\HotelController;
use App\Http\Controllers\Backend\Hotels\HotelChainController;
use App\Http\Controllers\Backend\Hotels\HotelMarketController;
use App\Http\Controllers\Backend\Hotels\HotelBrandController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\authentications\LoginBasic;
use App\Http\Controllers\authentications\RegisterBasic;
use App\Http\Controllers\language\LanguageController;
use App\Http\Controllers\pages\HomePage;
use App\Http\Controllers\pages\Page2;
use App\Http\Controllers\pages\MiscError;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\TeamController;
use App\Http\Controllers\UserController;

// Main Page Route
Route::get('/', [HomePage::class, 'index'])->name('pages-home');
Route::get('/page-2', [Page2::class, 'index'])->name('pages-page-2');

// locale
Route::get('/lang/{locale}', [LanguageController::class, 'swap']);
Route::get('/pages/misc-error', [MiscError::class, 'index'])->name('pages-misc-error');

// authentication
Route::get('/auth/login-basic', [LoginBasic::class, 'index'])->name('auth-login-basic');
Route::get('/auth/register-basic', [RegisterBasic::class, 'index'])->name('auth-register-basic');

// Microsoft OAuth routes
Route::get('/admin/oauth/microsoft', [\App\Http\Controllers\Auth\MicrosoftOAuthController::class, 'redirectToMicrosoft'])
    ->name('admin.oauth.microsoft');
//    ->middleware('auth');

	Route::get('/admin/oauth/microsoft/callback', [\App\Http\Controllers\Auth\MicrosoftOAuthController::class, 'handleMicrosoftCallback'])
	    ->name('admin.oauth.microsoft.callback');
	//    ->middleware('auth');

	// Microsoft OAuth status check
	Route::get('/admin/oauth/microsoft/status', [\App\Http\Controllers\Auth\SocialAuthController::class, 'checkMicrosoftStatus'])
	    ->name('admin.oauth.microsoft.status');

	// Microsoft OAuth token status check
	Route::get('/admin/oauth/microsoft/status/check', [\App\Http\Controllers\Auth\MicrosoftOAuthController::class, 'checkTokenStatus'])
	    ->name('admin.oauth.microsoft.status.check')
	    ->middleware('auth');

	// Microsoft Email Client routes
	Route::middleware(['auth'])->prefix('admin/emails')->name('admin.emails.')->group(function () {
		Route::get('/', [\App\Http\Controllers\EmailController::class, 'index'])->name('index');
		Route::get('/folders', [\App\Http\Controllers\EmailController::class, 'folders'])->name('folders');
		Route::get('/folder/{folder}', [\App\Http\Controllers\EmailController::class, 'folder'])->name('folder');
		Route::get('/view/{id}', [\App\Http\Controllers\EmailController::class, 'view'])->name('view');
		Route::get('/thread/{id}', [\App\Http\Controllers\EmailController::class, 'viewThread'])->name('thread');
		Route::post('/reply/{id}', [\App\Http\Controllers\EmailController::class, 'reply'])->name('reply');
		Route::post('/reply-all/{id}', [\App\Http\Controllers\EmailController::class, 'replyAll'])->name('reply-all');
		Route::post('/mark-as-read/{id}', [\App\Http\Controllers\EmailController::class, 'markAsRead'])->name('mark-as-read');
		Route::post('/mark-as-unread/{id}', [\App\Http\Controllers\EmailController::class, 'markAsUnread'])->name('mark-as-unread');

		Route::post('/toggle-conversation-view', [\App\Http\Controllers\EmailController::class, 'toggleConversationView'])->name('toggle-conversation-view');
	});




Route::group(['middleware' => ['auth', 'verified'], 'prefix' => 'admin'], function () {
//Route::group([ 'prefix' => 'admin'], function () {
	Route::get('/dashboard', [\App\Http\Controllers\DashboardController::class, 'index'])->name('admin.dashboard');
	Route::get('/dashboard2', [\App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');

	Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
	Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
	Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
	Route::post('/profile/avatar', [ProfileController::class, 'updateAvatar'])->name('profile.avatar');

	Route::get('/profile/connect/{provider}', [ProfileController::class, 'connectSocial'])->name('profile.social.connect');
	Route::get('/profile/connect/{provider}/callback', [ProfileController::class, 'handleSocialConnection'])->name('profile.social.callback');
	Route::post('/profile/disconnect-social', [ProfileController::class, 'disconnectSocial'])->name('profile.social.disconnect');

	Route::resource('teams', TeamController::class)->only(['index', 'create', 'store','update', 'edit']);
	Route::get('teams/change/{teamId}', [TeamController::class, 'changeCurrentTeam'])->name('teams.change');
	Route::get('teams/team-list', [TeamController::class, 'teamList'])->name('teams.list');
	Route::prefix('api/teams')->middleware('auth')->group(function () {
		Route::get('{team}', [TeamApiController::class, 'edit'])->name('teams.api.edit');
		Route::post('/', [TeamApiController::class, 'store'])->name('teams.api.store');
		Route::put('{team}', [TeamApiController::class, 'update'])->name('teams.api.update');
//		Route::post('/', [PostApiController::class, 'store']);
//		Route::put('{post}', [PostApiController::class, 'update']);
//		Route::delete('{post}', [PostApiController::class, 'destroy']);
	});

	Route::resource('hotel', HotelController::class)
		->only(['index', 'create', 'store','update', 'edit', 'show']);
	Route::post('hotel/{hotel}/delete-photo', [HotelController::class, 'deletePhoto'])->name('hotel.delete-photo');
	Route::get('hotels/map', [HotelController::class, 'map'])->name('admin.hotels.map');
 Route::match(['get', 'post'], 'hotels/actions', [HotelController::class, 'actions'])->name('admin.hotels.actions');
	Route::post('hotels/export/excel', [HotelController::class, 'exportExcel'])->name('admin.hotels.export.excel');
	Route::post('hotels/export/pdf', [HotelController::class, 'exportPdf'])->name('admin.hotels.export.pdf');
	Route::post('hotels/export/csv', [HotelController::class, 'exportCsv'])->name('admin.hotels.export.csv');
	Route::get('hotels/{id}/variables', [HotelController::class, 'getHotelVariables'])->name('admin.hotels.variables');
	Route::post('hotels/send-emails', [HotelController::class, 'sendEmails'])->name('admin.hotels.send-emails');

	Route::group(['prefix' => 'config'], function () {
		// Hotel Chain routes - restricted to master-admin and company-owner
		Route::middleware([\App\Http\Middleware\RoleMiddleware::class . ':master-admin,company-owner,agency-admin'])
			->group(function () {
				Route::resource('hotel-chains', HotelChainController::class);

				// Hotel Market routes
				Route::resource('hotel-markets', HotelMarketController::class);

				// Hotel Brand routes
				Route::resource('hotel-brands', HotelBrandController::class);
				Route::get('hotel-brands-markets-by-chain', [HotelBrandController::class, 'getMarketsByChain'])->name('hotel-brands.markets-by-chain');

				// Email Templates routes
				Route::resource('email-templates', \App\Http\Controllers\Backend\EmailTemplateController::class);
				Route::get('email-templates/{emailTemplate}/json', [\App\Http\Controllers\Backend\EmailTemplateController::class, 'getJson']);
				Route::post('email-templates/{emailTemplate}/duplicate', [\App\Http\Controllers\Backend\EmailTemplateController::class, 'duplicate'])->name('email-templates.duplicate');
			});

		// Menu Items routes - restricted to master-admin only
		Route::middleware([\App\Http\Middleware\RoleMiddleware::class . ':master-admin'])
			->group(function () {
				Route::resource('menu-items', \App\Http\Controllers\Admin\MenuItemController::class)->names([
					'index' => 'admin.menu-items.index',
					'create' => 'admin.menu-items.create',
					'store' => 'admin.menu-items.store',
					'edit' => 'admin.menu-items.edit',
					'update' => 'admin.menu-items.update',
					'destroy' => 'admin.menu-items.destroy',
				]);
				Route::post('menu-items/reorder', [\App\Http\Controllers\Admin\MenuItemController::class, 'reorder'])->name('admin.menu-items.reorder');
			});
	});

//	Route::get('/user-list', TeamController::class);

//	Route::resource('tasks', TaskController::class);

	Route::get('users/stop-impersonating', [UserController::class, 'stopImpersonating'])->name('users.stop-impersonating');
	Route::get('users/{user}/impersonate', [UserController::class, 'impersonate'])->name('users.impersonate');
	Route::resource('users', UserController::class);

	// Role management
	Route::resource('roles', \App\Http\Controllers\RoleController::class);


	Route::get('/cities-grouped', [HotelApiController::class, 'citiesGroupBySubregion'])->name('api.hotel-api.cities-grouped');
	Route::get('/countries', [HotelApiController::class, 'countries'])->name('api.hotel-api.countries');
	Route::get('/hotels-list', [HotelApiController::class, 'hotels'])->name('api.hotel-api.hotels-list');
	Route::get('/hotels-for-map', [HotelApiController::class, 'hotelsForMap'])->name('api.hotel-api.hotels-for-map');
	Route::get('/regions', [HotelApiController::class, 'regions'])->name('api.hotel-api.regions');
	Route::get('/regions-grouped', [HotelApiController::class, 'regionsGroupByCountry'])->name('api.hotel-api.regions-grouped');
	Route::get('/subregions', [HotelApiController::class, 'subregions'])->name('api.hotel-api.subregions');
	Route::get('/subregions-grouped', [HotelApiController::class, 'subregionsGroupByRegion'])->name('api.hotel-api.subregions-grouped');

	// Outgoing Emails routes
	Route::prefix('outgoing-emails')->name('admin.outgoing-emails.')->group(function () {
		Route::get('/', [\App\Http\Controllers\OutgoingEmailController::class, 'index'])->name('index');
		Route::get('/thread/{id}', [\App\Http\Controllers\OutgoingEmailController::class, 'viewThread'])->name('thread');
		Route::get('/conversation', [\App\Http\Controllers\OutgoingEmailController::class, 'searchByConversationId'])->name('conversation');
		Route::get('/hotels', [\App\Http\Controllers\OutgoingEmailController::class, 'getHotels'])->name('hotels');
		Route::get('/actions', [\App\Http\Controllers\OutgoingEmailController::class, 'getActions'])->name('actions');
	});

	// Actions routes
	Route::resource('actions', \App\Http\Controllers\ActionController::class)->names([
		'index' => 'admin.actions.index',
		'create' => 'admin.actions.create',
		'store' => 'admin.actions.store',
		'show' => 'admin.actions.show',
		'edit' => 'admin.actions.edit',
		'update' => 'admin.actions.update',
		'destroy' => 'admin.actions.destroy',
	]);
});

Route::get('/protected', function () {
	return response()->json(['message' => 'You are authenticated.']);
})->middleware('auth.basic');

require __DIR__.'/auth.php';
