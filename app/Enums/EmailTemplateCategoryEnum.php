<?php

namespace App\Enums;

enum EmailTemplateCategoryEnum: string
{
    case HOTEL_ACCOMMODATION = 'hotel.accommodation';
    case HOTEL_PROSPECT = 'hotel.prospect';

    /**
     * Get the label for the enum value.
     *
     * @return string
     */
    public function label(): string
    {
        return match($this) {
            self::HOTEL_ACCOMMODATION => 'Hotel Accommodation',
            self::HOTEL_PROSPECT => 'Hotel Prospect',
        };
    }

    /**
     * Get all enum values as an array for select options.
     *
     * @return array
     */
    public static function options(): array
    {
        return collect(self::cases())->map(function ($case) {
            return [
                'value' => $case->value,
                'label' => $case->label(),
            ];
        })->toArray();
    }
}
