<?php

namespace App\Models;

use App\Enums\RoleEnum as RoleEnum;
use Spatie\Permission\Models\Role as RoleModel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Team extends Model
{
	use HasFactory;

	protected $fillable = [
		'name',
	];

	public function users(): BelongsToMany
	{
//		return $this->belongsToMany(User::class)->withPivot('role');

		return $this->belongsToMany(
			User::class,
			config('permission.table_names.model_has_roles'),
			'team_id',
			'model_id'
		)->where('model_type', User::class);
	}

	public function getUserCount(): int
	{
		return $this->users()->count();
	}

	public function getUsersWithRoles()
	{
		return $this->users()
			->with('roles')
			->get()
			->map(function ($user) {
				return [
					'id' => $user->id,
					'name' => $user->name,
					'email' => $user->email,
					'roles' => $user->getRoleNames()
				];
			});
	}
}
