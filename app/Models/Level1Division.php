<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Level1Division extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'country_id'];

    protected $table = 'level_1_divisions';

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

	public function level2Divisions()
	{
		return $this->hasMany(Level2Division::class);
	}
}
