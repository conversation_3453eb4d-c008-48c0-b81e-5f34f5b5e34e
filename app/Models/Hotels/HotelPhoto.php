<?php

namespace App\Models\Hotels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HotelPhoto extends Model
{
    use HasFactory;

    protected $table = 'hotel_photos'; // Specificăm explicit tabelul

    protected $fillable = ['hotel_id', 'path_img', 'path_thumb', 'path_orig', 'default'];

    protected $casts = [
        'default' => 'boolean',
    ];

    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }
}
