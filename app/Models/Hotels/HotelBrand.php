<?php

namespace App\Models\Hotels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HotelBrand extends Model
{
    use HasFactory;

    protected $table = 'hotel_brand';
    protected $fillable = ['hotel_chain_id', 'hotel_market_id', 'name', 'icon'];

    public function chain()
    {
        return $this->belongsTo(HotelChain::class, 'hotel_chain_id');
    }

    public function market()
    {
        return $this->belongsTo(HotelMarket::class, 'hotel_market_id');
    }

    public function hotels()
    {
        return $this->hasMany(Hotel::class, 'hotel_brand_id');
    }
}
