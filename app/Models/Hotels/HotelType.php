<?php

namespace App\Models\Hotels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HotelType extends Model
{
    use HasFactory;

    protected $fillable = ['name'];

    // Relația Many-to-Many cu hotels
    public function hotels()
    {
        return $this->belongsToMany(Hotel::class, 'hotel_hotel_type');
    }

    // Relația One-to-Many cu hotels ca main_type
    public function mainHotels()
    {
        return $this->hasMany(Hotel::class, 'main_type_id');
    }
}
