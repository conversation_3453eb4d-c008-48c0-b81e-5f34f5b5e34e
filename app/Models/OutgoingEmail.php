<?php

namespace App\Models;

use App\Models\Hotels\Hotel;
use App\Models\Action;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OutgoingEmail extends Model
{
    use HasFactory;

    protected $fillable = [
        'hotel_id',
        'action_id',
        'user_id',
        'to_email',
        'subject',
        'body',
        'sent_at',
        'graph_message_id',
        'internet_message_id',
        'conversation_id',
        'references',
        'status',
        'error_message'
    ];

    protected $casts = [
        'sent_at' => 'datetime',
    ];

    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }

    public function action()
    {
        return $this->belongsTo(Action::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
