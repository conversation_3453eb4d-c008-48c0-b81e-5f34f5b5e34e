<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Country;
use App\Models\Level1Division;
use App\Models\Level2Division;

class City extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'country_id', 'level_1_division_id', 'level_2_division_id', 'latitude', 'longitude'];

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function level1Division()
    {
        return $this->belongsTo(Level1Division::class, 'level_1_division_id');
    }

    public function level2Division()
    {
        return $this->belongsTo(Level2Division::class, 'level_2_division_id');
    }
}
