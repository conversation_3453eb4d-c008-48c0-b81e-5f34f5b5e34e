<?php

namespace App\Http\Controllers;

use App\Enums\PermissionEnum;
use App\Enums\RoleEnum as RoleEnum;
use App\Models\Team;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Illuminate\Support\Facades\Gate;
use Illuminate\Http\RedirectResponse;
use App\Http\Requests\StoreTeamRequest;
use Symfony\Component\HttpFoundation\Response;
use Spatie\Permission\Models\Role as RoleModel;

class TeamController extends Controller
{
    public function index(): View
    {
        Gate::authorize('viewAny', Team::class);

        $teams = Team::where('name', '!=', 'Master Admin Team')->paginate();

        return view('content.pages.admin.teams.index', compact('teams'));
    }

    public function create(): View
    {
        Gate::authorize('create', Team::class);

        $users = User::whereRelation('rolesWithoutTeam', 'name', '=', RoleEnum::ClinicOwner->value)
            ->pluck('name', 'id');

        return view('teams.create', compact('users'));
    }

    public function store(StoreTeamRequest $request): RedirectResponse
    {
        Gate::authorize('create', Team::class);

        $team = Team::create(['name' => $request->input('clinic_name')]);

        if ($request->integer('user_id') > 0) {
            $user = User::find($request->integer('user_id'));
            $user->update(['current_team_id' => $team->id]);
        } else {
            $user = User::create($request->only(['name', 'email', 'password'])
                + ['current_team_id' => $team->id]);
        }

        $user->teams()
            ->attach($team->id, [
                'model_type' => User::class,
                'role_id'    => RoleModel::where('name', RoleEnum::ClinicOwner->value)->first()->id,
            ]);

        return redirect()->route('teams.index');
    }

	public function edit(Request $request, Team $team): View
	{
		Gate::authorize('update', $team);

		$users = User::whereRelation('rolesWithoutTeam', 'name', '=', RoleEnum::ClinicOwner->value)
			->pluck('name', 'id');

		return view('teams.edit', compact('team', 'users'));
	}

	/**
	 * @param int $teamId
	 * @return RedirectResponse
	 */
    public function changeCurrentTeam(int $teamId): RedirectResponse
    {
        Gate::authorize('changeTeam', Team::class);

        $team = auth()->user()->teams()->findOrFail($teamId);

        if (! auth()->user()->belongsToTeam($team)) {
            abort(Response::HTTP_FORBIDDEN);
        }

        // Change teams
        auth()->user()->update(['current_team_id' => $team->id]);
        setPermissionsTeamId($team->id);
        auth()->user()->unsetRelation('roles')->unsetRelation('permissions');

        return redirect(route('admin.dashboard'), Response::HTTP_SEE_OTHER);
    }

	/**
	 * Display a listing of the resource.
	 *
	 */
	public function teamList(Request $request): JsonResponse
	{
		Gate::authorize(PermissionEnum::EDIT_TEAM, Team::class);

		$columns = [
			1 => 'id',
			2 => 'name',
			3 => 'count_user',
		];

		$search = [];

		$totalData = Team::where('name', '!=', 'Master Admin Team')
			->count();

		$totalFiltered = $totalData;

		$limit = $request->input('length');
		$start = $request->input('start');
		$order = $columns[$request->input('order.0.column')];
		$dir = $request->input('order.0.dir');

		if (empty($request->input('search.value'))) {
			$teams = Team::offset($start)
				->withCount('users')
				->where('name', '!=', 'Master Admin Team')
				->limit($limit)
				->orderBy($order, $dir)
				->get();
		} else {
			$search = $request->input('search.value');

			$teams = Team::where('name', 'LIKE', "%{$search}%")
				->where('name', '!=', 'Master Admin Team')
				->withCount('users')
				->offset($start)
				->limit($limit)
				->orderBy($order, $dir)
				->get();

			$totalFiltered = Team::where('name', 'LIKE', "%{$search}%")
				->count();
		}

		$data = [];

		if (!empty($teams)) {
			// providing a dummy id instead of database ids
			$ids = $start;

			foreach ($teams as $team) {
				$nestedData['id'] = $team->id;
				$nestedData['fake_id'] = ++$ids;
				$nestedData['name'] = $team->name;
				$nestedData['user_count'] = $team->users_count;
				$nestedData['email'] = '';
				$nestedData['email_verified_at'] = null;

				$data[] = $nestedData;
			}
		}

		return response()->json([
			'draw' => (int)$request->input('draw'),
			'recordsTotal' => (int)$totalData,
			'recordsFiltered' => (int)$totalFiltered,
			'code' => 200,
			'data' => $data,
		]);
	}
}
