<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\MicrosoftOAuthToken;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;

class MicrosoftOAuthController extends Controller
{
	/**
	 * Redirect the user to the Microsoft authentication page.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\RedirectResponse
	 */
	public function redirectToMicrosoft(Request $request)
	{
		Log::info('Microsoft OAuth redirectToMicrosoft init method');

		// Generate a random state parameter to prevent CSRF attacks
		$state = Str::random(40);

		// Store the state in the session
		session(['microsoft_oauth_state' => $state]);

		// Store the return URL in the session if provided
		$returnUrl = $request->query('return_url');
		if ($returnUrl) {
			session(['microsoft_oauth_return_url' => $returnUrl]);
			Log::info('Microsoft OAuth return URL stored in session', [
				'return_url' => $returnUrl,
				'user_id' => Auth::id()
			]);
		}

		// Log the state for debugging
		Log::info('Microsoft OAuth state generated and stored in session', [
			'state' => $state,
			'user_id' => Auth::id()
		]);

		// Define the scopes we need
		$scopes = [
			'offline_access',  // Required for refresh tokens
			'User.Read',       // Basic user profile
			'Mail.Send',       // Send mail as the user
			'Mail.Read',       // Read user mail
			'Mail.ReadWrite',  // Read and write access to user mail
			'Mail.ReadBasic',  // Read basic mail properties
		];

		// Build the authorization URL
		$authUrl = 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize';
		$queryParams = [
			'client_id' => config('services.microsoft.client_id'),
			'response_type' => 'code',
			'redirect_uri' => config('services.microsoft.redirect'),
			'response_mode' => 'query',
			'scope' => implode(' ', $scopes),
			'state' => $state,
			'prompt' => 'select_account', // Force account selection even if user is already logged in
		];

		$authorizationUrl = $authUrl . '?' . http_build_query($queryParams);

		return redirect()->away($authorizationUrl);
	}

	/**
	 * Handle the callback from Microsoft.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\RedirectResponse
	 */
	public function handleMicrosoftCallback(Request $request)
	{
		// Check state to prevent CSRF attacks
		$state = session('microsoft_oauth_state');

		// Log the state values for debugging
		Log::info("Microsoft OAuth callback received", [
			'session_state' => $state,
			'request_state' => $request->state,
			'user_id' => Auth::id(),
			'all_session_data' => session()->all()
		]);

		if (!$state || $state !== $request->state) {
			Log::warning("Invalid state parameter. Authentication failed.", [
				'session_state' => $state,
				'request_state' => $request->state,
				'user_id' => Auth::id()
			]);

			// Regenerate the state and try again
			return redirect()->route('admin.oauth.microsoft')
				->with('error', 'Invalid state parameter. Please try again.');
		}

		// Check for error
		if ($request->has('error')) {
			Log::error("Microsoft authentication error", [
				'error' => $request->error,
				'error_description' => $request->error_description,
				'user_id' => Auth::id()
			]);

			// Check if we have a return URL in the session
			$returnUrl = session('microsoft_oauth_return_url');
			if ($returnUrl) {
				// Clear the return URL from the session
				session()->forget('microsoft_oauth_return_url');

				Log::info('Redirecting to stored return URL after Microsoft OAuth error', [
					'return_url' => $returnUrl,
					'user_id' => Auth::id()
				]);

				// Redirect to the stored return URL with error message
				return redirect($returnUrl)
					->with('error', 'Microsoft authentication failed: ' . $request->error_description);
			}

			// Default redirect if no return URL is stored
			return redirect()->route('pages-home')
				->with('error', 'Microsoft authentication failed: ' . $request->error_description);
		}

		try {
			// Exchange authorization code for tokens
			$response = Http::asForm()->post('https://login.microsoftonline.com/common/oauth2/v2.0/token', [
				'client_id' => config('services.microsoft.client_id'),
				'client_secret' => config('services.microsoft.client_secret'),
				'grant_type' => 'authorization_code',
				'code' => $request->code,
				'redirect_uri' => config('services.microsoft.redirect'),
			]);

			if ($response->failed()) {
				Log::error('Microsoft token exchange failed', [
					'error' => $response->body(),
					'status' => $response->status(),
					'user_id' => Auth::id()
				]);

				// Check if we have a return URL in the session
				$returnUrl = session('microsoft_oauth_return_url');
				if ($returnUrl) {
					// Clear the return URL from the session
					session()->forget('microsoft_oauth_return_url');

					Log::info('Redirecting to stored return URL after token exchange error', [
						'return_url' => $returnUrl,
						'user_id' => Auth::id()
					]);

					// Redirect to the stored return URL with error message
					return redirect($returnUrl)
						->with('error', 'Failed to exchange authorization code for tokens.');
				}

				// Default redirect if no return URL is stored
				return redirect()->route('pages-home')
					->with('error', 'Failed to exchange authorization code for tokens.');
			}

			$tokenData = $response->json();

			Log::info('Microsoft token exchange successful', [
				'token_type' => $tokenData['token_type'] ?? 'unknown',
				'expires_in' => $tokenData['expires_in'] ?? 0,
				'scope' => $tokenData['scope'] ?? 'unknown',
				'user_id' => Auth::id()
			]);

			// Get user info from Microsoft Graph API
			$userResponse = Http::withToken($tokenData['access_token'])
				->get('https://graph.microsoft.com/v1.0/me');

			if ($userResponse->failed()) {
				Log::error('Failed to get Microsoft user info', [
					'error' => $userResponse->body(),
					'status' => $userResponse->status(),
					'user_id' => Auth::id()
				]);

				// Check if we have a return URL in the session
				$returnUrl = session('microsoft_oauth_return_url');
				if ($returnUrl) {
					// Clear the return URL from the session
					session()->forget('microsoft_oauth_return_url');

					Log::info('Redirecting to stored return URL after user info error', [
						'return_url' => $returnUrl,
						'user_id' => Auth::id()
					]);

					// Redirect to the stored return URL with error message
					return redirect($returnUrl)
						->with('error', 'Failed to get user information from Microsoft.');
				}

				// Default redirect if no return URL is stored
				return redirect()->route('pages-home')
					->with('error', 'Failed to get user information from Microsoft.');
			}

			$userInfo = $userResponse->json();

			Log::info('Microsoft user info retrieved', [
				'microsoft_user_id' => $userInfo['id'] ?? 'unknown',
				'display_name' => $userInfo['displayName'] ?? 'unknown',
				'email' => $userInfo['mail'] ?? $userInfo['userPrincipalName'] ?? 'unknown',
				'user_id' => Auth::id()
			]);

//Log::info('User response', [
//	'user_info' => $userInfo,
//	'user_id' => Auth::id()
//]);
//$user = null;

			// Check if user exists
			$user = User::where('provider_id', $userInfo['id'])
				->where('provider', 'microsoft')
				->first();

			// If user doesn't exist, search by email and update
			if (!$user) {
				// Check if email already exists
				$existingUser = User::where('email', $userInfo['mail'])->first();

				if ($existingUser) {
					// Link social account to existing user
					$existingUser->provider = 'microsoft';
					$existingUser->provider_id = $userInfo['id'];
					$existingUser->save();

					$user = $existingUser;


				} else {
					// User doesn't exist in our system, so we don't allow them to register automatically
					return redirect('/login')->withErrors([
						'login' => 'Your email is not authorized to access this platform.'
					]);

				}
			}

			// Login
			Auth::login($user);

			Log::error('Avem user autentificat?', [
				'user_id' => Auth::id(),
				'user_name' => Auth::user()->name,
				'user_email' => Auth::user()->email,
				'is_admin' => Auth::user()->hasRole('master-admin'),
			]);

			// ====================


			// Update user's provider information
//			$user = Auth::user();
//			if (!$user) {
//				Log::error('No authenticated user found during Microsoft OAuth callback');
//				return redirect()->route('login')
//					->with('error', 'You must be logged in to connect your Microsoft account.');
//			}
//
//			$user->provider = 'microsoft';
//			$user->provider_id = $userInfo['id'];
//			$user->save();

			// Deactivate any existing tokens for this user
			MicrosoftOAuthToken::where('user_id', $user->id)
				->update(['is_active' => false]);

			// Calculate expiration time
			$expiresAt = now()->addSeconds($tokenData['expires_in']);

			// Create a new token record with all the data from the response
			$token = MicrosoftOAuthToken::create([
				'user_id' => $user->id,
				'access_token' => $tokenData['access_token'],
				'refresh_token' => $tokenData['refresh_token'],
				'token_type' => $tokenData['token_type'] ?? 'Bearer',
				'scopes' => explode(' ', $tokenData['scope']),
				'expires_at' => $expiresAt,
				'is_active' => true,
			]);

			// Log successful token storage
			Log::info('Microsoft OAuth token stored successfully', [
				'user_id' => $user->id,
				'token_id' => $token->id,
				'expires_at' => $expiresAt->format('Y-m-d H:i:s'),
				'scopes' => explode(' ', $tokenData['scope']),
			]);

			// Clear the state from the session
			session()->forget('microsoft_oauth_state');

			// Check if we have a return URL in the session
			$returnUrl = session('microsoft_oauth_return_url');
			if ($returnUrl) {
				// Clear the return URL from the session
				session()->forget('microsoft_oauth_return_url');

				Log::info('Redirecting to stored return URL after Microsoft OAuth', [
					'return_url' => $returnUrl,
					'user_id' => Auth::id()
				]);

				// Redirect to the stored return URL
				return redirect($returnUrl)
					->with('success', 'Microsoft account connected successfully.');
			}

			// Default redirect if no return URL is stored
			return redirect()->route('pages-home')
				->with('success', 'Microsoft account connected successfully.');
		} catch (\Exception $e) {
			Log::error('Microsoft authentication error', [
				'error' => $e->getMessage(),
				'trace' => $e->getTraceAsString(),
				'user_id' => Auth::id()
			]);

			// Check if we have a return URL in the session
			$returnUrl = session('microsoft_oauth_return_url');
			if ($returnUrl) {
				// Clear the return URL from the session
				session()->forget('microsoft_oauth_return_url');

				Log::info('Redirecting to stored return URL after Microsoft OAuth error', [
					'return_url' => $returnUrl,
					'user_id' => Auth::id()
				]);

				// Redirect to the stored return URL with error message
				return redirect($returnUrl)
					->with('error', 'An error occurred during Microsoft authentication: ' . $e->getMessage());
			}

			// Default redirect if no return URL is stored
			return redirect()->route('pages-home')
				->with('error', 'An error occurred during Microsoft authentication: ' . $e->getMessage());
		}
	}

	/**
	 * Store or update a Microsoft OAuth token for a user.
	 *
	 * @param int $userId
	 * @param string $accessToken
	 * @param string $refreshToken
	 * @param int $expiresIn
	 * @param array $scopes
	 * @return \App\Models\MicrosoftOAuthToken
	 */
	protected function storeOrUpdateToken(int $userId, string $accessToken, string $refreshToken, int $expiresIn, array $scopes)
	{
		// Deactivate any existing tokens for this user
		MicrosoftOAuthToken::where('user_id', $userId)
			->update(['is_active' => false]);

		// Calculate expiration time
		$expiresAt = now()->addSeconds($expiresIn);

		// Create a new token
		return MicrosoftOAuthToken::create([
			'user_id' => $userId,
			'access_token' => $accessToken,
			'refresh_token' => $refreshToken,
			'expires_at' => $expiresAt,
			'scopes' => $scopes,
			'is_active' => true,
		]);
	}

	/**
	 * Disconnect the user's Microsoft account.
	 *
	 * @return \Illuminate\Http\RedirectResponse
	 */
	public function disconnect()
	{
		// Deactivate all tokens for the current user
		MicrosoftOAuthToken::where('user_id', Auth::id())
			->update(['is_active' => false]);

		return redirect()->route('pages-home')->with('success', 'Microsoft account disconnected successfully.');
	}

	/**
	 * Check the status of the Microsoft OAuth token for the current user.
	 *
	 * @return \Illuminate\Http\JsonResponse
	 */
	public function checkTokenStatus()
	{
		$user = Auth::user();
		if (!$user) {
			return response()->json([
				'status' => 'error',
				'message' => 'User not authenticated',
			], 401);
		}

		$token = MicrosoftOAuthToken::getActiveTokenForUser($user->id);

		if (!$token) {
			return response()->json([
				'status' => 'error',
				'message' => 'No active Microsoft OAuth token found',
				'oauth_required' => true,
				'oauth_url' => route('admin.oauth.microsoft'),
			]);
		}

		// Check if token is expired
		$isExpired = $token->isExpired();
		$isAboutToExpire = $token->isAboutToExpire();

		// Try to refresh if needed
		$refreshed = false;
		if ($isExpired || $isAboutToExpire) {
			$microsoftGraphService = app(\App\Services\MicrosoftGraphService::class);
			$refreshed = $microsoftGraphService->refreshToken($token);
		}

		return response()->json([
			'status' => 'success',
			'token_exists' => true,
			'token_id' => $token->id,
			'is_expired' => $isExpired,
			'is_about_to_expire' => $isAboutToExpire,
			'expires_at' => $token->expires_at->toIso8601String(),
			'refreshed' => $refreshed,
			'scopes' => $token->scopes,
		]);
	}
}
