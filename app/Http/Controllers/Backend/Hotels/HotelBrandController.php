<?php

namespace App\Http\Controllers\Backend\Hotels;

use App\Http\Controllers\Controller;
use App\Models\Hotels\HotelBrand;
use App\Models\Hotels\HotelChain;
use App\Models\Hotels\HotelMarket;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class HotelBrandController extends Controller
{
    /**
     * Display a listing of the hotel brands.
     */
    public function index(): View
    {
        $brands = HotelBrand::with(['chain', 'market'])
            ->withCount('hotels')
            ->get();

        return view('backend.hotels.brands.index', compact('brands'));
    }

    /**
     * Show the form for creating a new hotel brand.
     */
    public function create(): View
    {
        $chains = HotelChain::all();
        $markets = HotelMarket::all();

        return view('backend.hotels.brands.create', compact('chains', 'markets'));
    }

    /**
     * Store a newly created hotel brand in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'hotel_chain_id' => 'required|exists:hotel_chain,id',
            'hotel_market_id' => 'required|exists:hotel_market,id',
            'icon' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);

        // Handle icon upload
        if ($request->hasFile('icon')) {
            $iconPath = $request->file('icon')->store('brands', 'public');
            $validated['icon'] = $iconPath;
        }

        $brand = HotelBrand::create($validated);

        return redirect()->route('hotel-brands.index')
            ->with('success', 'Hotel brand created successfully.');
    }

    /**
     * Display the specified hotel brand.
     */
    public function show(HotelBrand $hotelBrand): View
    {
        $hotelBrand->load(['chain', 'market', 'hotels']);

        return view('backend.hotels.brands.show', compact('hotelBrand'));
    }

    /**
     * Show the form for editing the specified hotel brand.
     */
    public function edit(HotelBrand $hotelBrand): View
    {
        $chains = HotelChain::all();
        $markets = HotelMarket::all();

        return view('backend.hotels.brands.edit', compact('hotelBrand', 'chains', 'markets'));
    }

    /**
     * Update the specified hotel brand in storage.
     */
    public function update(Request $request, HotelBrand $hotelBrand): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'hotel_chain_id' => 'required|exists:hotel_chain,id',
            'hotel_market_id' => 'required|exists:hotel_market,id',
            'icon' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);

        // Handle icon upload
        if ($request->hasFile('icon')) {
            // Delete old icon if exists
            if ($hotelBrand->icon) {
                Storage::disk('public')->delete($hotelBrand->icon);
            }

            $iconPath = $request->file('icon')->store('brands', 'public');
            $validated['icon'] = $iconPath;
        }

        $hotelBrand->update($validated);

        return redirect()->route('hotel-brands.index')
            ->with('success', 'Hotel brand updated successfully.');
    }

    /**
     * Remove the specified hotel brand from storage.
     */
    public function destroy(HotelBrand $hotelBrand): RedirectResponse
    {
        try {
            // Check if the brand has related hotels
            if ($hotelBrand->hotels()->count() > 0) {
                return redirect()->route('hotel-brands.index')
                    ->with('error', 'Cannot delete hotel brand with related hotels.');
            }

            // Delete icon if exists
            if ($hotelBrand->icon) {
                Storage::disk('public')->delete($hotelBrand->icon);
            }

            $hotelBrand->delete();

            return redirect()->route('hotel-brands.index')
                ->with('success', 'Hotel brand deleted successfully.');
        } catch (\Exception $e) {
            Log::error('Error deleting hotel brand: ' . $e->getMessage());

            return redirect()->route('hotel-brands.index')
                ->with('error', 'An error occurred while deleting the hotel brand.');
        }
    }

    /**
     * Get markets for a specific chain (for dynamic dropdown).
     */
    public function getMarketsByChain(Request $request): JsonResponse
    {
        $chainId = $request->input('chain_id');
        \Illuminate\Support\Facades\Log::info('getMarketsByChain called with chain_id: ' . $chainId);

        $markets = HotelMarket::where('hotel_chain_id', $chainId)->get();
        \Illuminate\Support\Facades\Log::info('Markets found: ' . $markets->count());

        return response()->json($markets);
    }
}
