<?php

namespace App\Http\Controllers;

use App\Enums\PermissionEnum;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleController extends Controller
{
    public function index(): View
    {
        // Gate::authorize('viewAny', Role::class);
        
        $roles = Role::with('permissions')->get();
        return view('roles.index', compact('roles'));
    }
    
    public function create(): View
    {
        // Gate::authorize('create', Role::class);
        
        $permissions = Permission::all();
        return view('roles.create', compact('permissions'));
    }
    
    public function store(Request $request): RedirectResponse
    {
        // Gate::authorize('create', Role::class);
        
        $request->validate([
            'name' => 'required|unique:roles,name',
            'permissions' => 'array',
        ]);
        
        $role = Role::create(['name' => $request->name]);
        $role->syncPermissions($request->permissions);
        
        return redirect()->route('roles.index')->with('success', 'Role created successfully');
    }
    
    public function edit(Role $role): View
    {
        // Gate::authorize('update', $role);
        
        $permissions = Permission::all();
        $rolePermissions = $role->permissions->pluck('id')->toArray();
        
        return view('roles.edit', compact('role', 'permissions', 'rolePermissions'));
    }
    
    public function update(Request $request, Role $role): RedirectResponse
    {
        // Gate::authorize('update', $role);
        
        $request->validate([
            'name' => 'required|unique:roles,name,' . $role->id,
            'permissions' => 'array',
        ]);
        
        $role->update(['name' => $request->name]);
        $role->syncPermissions($request->permissions);
        
        return redirect()->route('roles.index')->with('success', 'Role updated successfully');
    }
    
    public function destroy(Role $role): RedirectResponse
    {
        // Gate::authorize('delete', $role);
        
        $role->delete();
        
        return redirect()->route('roles.index')->with('success', 'Role deleted successfully');
    }
}