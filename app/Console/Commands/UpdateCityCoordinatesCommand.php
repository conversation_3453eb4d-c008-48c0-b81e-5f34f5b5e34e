<?php

namespace App\Console\Commands;

use App\Models\City;
use App\Services\GoogleMapsService;
use Illuminate\Console\Command;

class UpdateCityCoordinatesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cities:update-coordinates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update coordinates for cities that do not have latitude and longitude set';

    /**
     * Execute the console command.
     */
    public function handle(GoogleMapsService $googleMapsService)
    {
        $this->info('Starting to update city coordinates...');

        // Get all cities without coordinates
        $cities = City::whereNull('latitude')
            ->orWhereNull('longitude')
            ->get();

        $totalCities = $cities->count();
        $this->info("Found {$totalCities} cities without coordinates.");

        if ($totalCities === 0) {
            $this->info('No cities need updating. Exiting.');
            return 0;
        }

        $updatedCount = 0;
        $failedCount = 0;

        // Create a progress bar
        $bar = $this->output->createProgressBar($totalCities);
        $bar->start();

        foreach ($cities as $city) {
            $result = $googleMapsService->updateCityCoordinatesIfNeeded($city);
            
            if ($result) {
                $updatedCount++;
            } else {
                $failedCount++;
                $this->newLine();
                $this->warn("Failed to update coordinates for city ID: {$city->id}, Name: {$city->name}");
            }
            
            $bar->advance();
            
            // Add a small delay to avoid hitting API rate limits
            usleep(200000); // 200ms delay
        }

        $bar->finish();
        $this->newLine(2);
        
        $this->info("Completed updating city coordinates:");
        $this->info("- Updated: {$updatedCount} cities");
        $this->info("- Failed: {$failedCount} cities");
        
        return 0;
    }
}