<?php

namespace App\Console\Commands;

use App\Helpers\BKHelper;
use App\Jobs\NotifyAdmin;
use App\Jobs\NotifyAdminOfCompletedAttomCsvImport;
use App\Jobs\NotifyUserOfCompletedImport;
use App\Models\Attom\AttomImportArchive;
use App\Models\Attom\AttomImportFile;
use App\Models\Attom\BKImportFile;
use App\Models\System\LastImport;
use App\Services\HotelImportService;
use App\User;
use Excel;
use Illuminate\Console\Command;
use PhpOffice\PhpSpreadsheet\Reader\Exception;

class HotelImportFlag extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'hotel:import:flag {--action=}';

    private $action;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Returneaza daca importul e blocat sau sterge flagul de blocare';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->action = $this->option('action');

        $hotelImportService = new HotelImportService();

        switch ($this->action) {
            case 'status':
                if ($hotelImportService->isImportLocked()) {
                    $this->info("Import is locked!");
                } else {
                    $this->info("Import is not locked!");
                }
                break;
            case 'remove':
                $hotelImportService->unlockImport();
                $this->info("Import lock removed!");
                break;
            default:
                $this->warn("Invalid action status. Allowed values: status or remove. Sintax example: 'hotel:import:flag --action=status'");
                break;
        }

        return 0;
    }
}

