<?php

namespace App\Services;

use App\Models\Hotel;
use App\Models\City;
use App\Services\GoogleMapsService;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class HotelFilterService
{
    /**
     * @var GoogleMapsService
     */
    protected $googleMapsService;

    /**
     * Constructor
     * 
     * @param GoogleMapsService $googleMapsService
     */
    public function __construct(GoogleMapsService $googleMapsService)
    {
        $this->googleMapsService = $googleMapsService;
    }
	/**
	 * Filter hotels based on various criteria
	 * 
	 * @param array $filters
	 * @return LengthAwarePaginator
	 */
	public function filterHotels(array $filters): LengthAwarePaginator
	{
		$query = \App\Models\Hotels\Hotel::query();

		// Filtrare simplă pe câmpuri directe
		if (!empty($filters['hotel_name'])) {
			$query->where('name', 'like', '%' . $filters['hotel_name'] . '%');
		}

		if (!empty($filters['price']) && is_array($filters['price'])) {
			$query->whereIn('price_range', $filters['price']);
		}

//		if (!empty($filters['status']) && is_array($filters['status'])) {
//			$query->whereIn('status', $filters['status']);
//		}

		// Relații: presupunem că Hotel are câmpuri gen country_id, region_id etc.
		if (!empty($filters['country']) && is_array($filters['country'])) {
			$query->whereIn('country_id', $filters['country']);
		}

		if (!empty($filters['region']) && is_array($filters['region'])) {
			$query->whereIn('level_1_division_id', $filters['region']);
		}

		if (!empty($filters['subregion']) && is_array($filters['subregion'])) {
			$query->whereIn('level_2_division_id', $filters['subregion']);
		}

		if (!empty($filters['city']) && is_array($filters['city'])) {
			$query->whereIn('city_id', $filters['city']);
		}

		// Paginare
		$perPage = $filters['perPage'] ?? 50;
		$page = $filters['page'] ?? null;
//		dd($query->toRawSql());
		return $query->paginate($perPage, ['*'], 'page', $page);
	}

	/**
	 * Filter hotels for map display based on various criteria including distance from city center
	 * 
	 * This method filters hotels based on various criteria, including the distance from the city center (kilometer zero)
	 * to each hotel. The city center coordinates are obtained from the cities.latitude and cities.longitude columns.
	 * Hotels are filtered to be within the range specified by min_distance_km and max_distance_km parameters.
	 * 
	 * @param array $filters
	 * @return array
	 */
	public function filterHotelsForMap(array $filters): array
	{
		$city = \App\Models\City::find($filters['city_id'] ?? null);
		if (!$city) {
			return [];
		}

		if (is_null($city->latitude) || is_null($city->longitude)) {
			if (!$this->googleMapsService->updateCityCoordinatesIfNeeded($city)) {
				return [];
			}
		}

		$lat = (float) $city->latitude;
		$lng = (float) $city->longitude;
		$minDistance = (float) ($filters['min_distance_km'] ?? 0);
		$maxDistance = (float) ($filters['max_distance_km'] ?? PHP_FLOAT_MAX);

		$haversineFormula = $this->computeHaversine($lat, $lng);

		$query = \App\Models\Hotels\Hotel::query()
			->select('*')
			->selectRaw("$haversineFormula AS distance_km")
			->whereNotNull('latitude')
			->whereNotNull('longitude')
			->with(['chain', 'brand', 'market', 'photos', 'contacts'])
			->when($filters['hotel_name'] ?? null, fn($q, $v) => $q->where('name', 'like', "%$v%"))
			->when($filters['min_price'] ?? null, fn($q, $v) => $q->where('recent_avg_price_per_person', '>=', $v))
			->when($filters['max_price'] ?? null, fn($q, $v) => $q->where('recent_avg_price_per_person', '<=', $v))
			->when(!empty($filters['stars']), fn($q) => $q->whereIn('stars', $filters['stars']))
			->when(!empty($filters['partner_type']), fn($q) => $q->whereIn('partner_type', $filters['partner_type']))
			->when(!empty($filters['hotel_chain']), fn($q) => $q->whereIn('hotel_chain_id', $filters['hotel_chain']))
			->when(!empty($filters['hotel_brand']), fn($q) => $q->whereIn('hotel_brand_id', $filters['hotel_brand']))
			->when(($filters['is_claimed'] ?? null) === '1', fn($q) => $q->where('is_claimed', 'Yes'))
			->when($filters['breakfast'] ?? null, fn($q) => $q->where('breakfast', 1))
			->when($filters['minimum_capacity'] ?? null, fn($q, $v) => $q->where('capacity_guests', '>=', $v))
			->when($filters['accept_groups'] ?? null, fn($q) => $q->where('accept_groups', 1))
			->when(!empty($filters['emails_filter']), function ($q) use ($filters) {
				return match ($filters['emails_filter']) {
					'default_emails' => $q->whereHas('contacts', fn($q) => $q->where('default', 1)->whereNotNull('email')),
					'any_emails'     => $q->whereHas('contacts', fn($q) => $q->whereNotNull('email')),
					'no_emails'      => $q->whereDoesntHave('contacts', fn($q) => $q->whereNotNull('email')),
					default          => $q,
				};
			})
			->havingRaw("distance_km BETWEEN ? AND ?", [$minDistance, $maxDistance])
			->orderBy('distance_km');

		$hotels = $query->get();

		return $hotels->map(fn($hotel) => [
			'id' => $hotel->id,
			'name' => $hotel->name,
			'latitude' => $hotel->latitude,
			'longitude' => $hotel->longitude,
			'price' => $hotel->recent_avg_price_per_person,
			'stars' => $hotel->stars,
			'partner_type' => $hotel->partner_type,
			'hotel_chain' => optional($hotel->chain)->name,
			'hotel_brand' => optional($hotel->brand)->name,
			'hotel_market' => optional($hotel->market)->name,
			'address' => $hotel->full_address,
			'email' => $this->getDefaultHotelContactEmails($hotel),
			'phone' => $hotel->phone_international,
			'website' => $hotel->website,
			'accept_groups' => $hotel->accept_groups,
			'breakfast' => $hotel->breakfast,
			'capacity_rooms' => $hotel->capacity_rooms,
			'capacity_guests' => $hotel->capacity_guests,
			'notes' => $hotel->notes,
			'image_url' => $this->getHotelImageUrl($hotel),
			'distance_km' => $hotel->distance_km,
			'reviews_rating' => $hotel->reviews_rating ?? 0,
			'reviews_count' => $hotel->reviews_count ?? 0,
		])->values()->all();
	}

	/**
	 * Count hotels with all filters applied including distance filtering
	 * Used to check if there are too many results before fetching all data
	 * 
	 * This method counts the number of hotels that match all filter criteria including distance filtering.
	 * It's used to check if there are too many results before fetching all the data.
	 * The distance filter calculates the distance from the city center (kilometer zero) to each hotel 
	 * and filters hotels based on the min_distance_km and max_distance_km parameters.
	 * 
	 * @param array $filters
	 * @return int
	 */
	public function countFilteredHotels(array $filters): int
	{
		$city = \App\Models\City::find($filters['city_id'] ?? null);
		if (!$city) {
			return 0;
		}

		if ($city->latitude === null || $city->longitude === null) {
			$updated = $this->googleMapsService->updateCityCoordinatesIfNeeded($city);
			if (!$updated || $city->latitude === null || $city->longitude === null) {
				return 0;
			}
		}

		$lat = (float) $city->latitude;
		$lng = (float) $city->longitude;

		$minDistance = isset($filters['min_distance_km']) ? (float)$filters['min_distance_km'] : 0;
		$maxDistance = isset($filters['max_distance_km']) ? (float)$filters['max_distance_km'] : PHP_FLOAT_MAX;

		// formula haversine in SQL pentru a filtra la nivel de DB
		$haversine = $this->computeHaversine($lat, $lng);
		// "(6371 * acos(cos(radians($lat)) * cos(radians(latitude)) * cos(radians(longitude) - radians($lng)) + sin(radians($lat)) * sin(radians(latitude))))";

		return \App\Models\Hotels\Hotel::query()
			->whereNotNull('latitude')
			->whereNotNull('longitude')
			->when($filters['hotel_name'] ?? null, fn($q, $v) => $q->where('name', 'like', "%$v%"))
			->when($filters['min_price'] ?? null, fn($q, $v) => $q->where('recent_avg_price_per_person', '>=', $v))
			->when($filters['max_price'] ?? null, fn($q, $v) => $q->where('recent_avg_price_per_person', '<=', $v))
			->when(!empty($filters['stars']), fn($q) => $q->whereIn('stars', $filters['stars']))
			->when(!empty($filters['partner_type']), fn($q) => $q->whereIn('partner_type', $filters['partner_type']))
			->when(!empty($filters['hotel_chain']), fn($q) => $q->whereIn('hotel_chain_id', $filters['hotel_chain']))
			->when(!empty($filters['hotel_brand']), fn($q) => $q->whereIn('hotel_brand_id', $filters['hotel_brand']))
			->when(($filters['is_claimed'] ?? null) === '1', fn($q) => $q->where('is_claimed', 'Yes'))
			->when($filters['breakfast'] ?? null, fn($q) => $q->where('breakfast', 1))
			->when($filters['minimum_capacity'] ?? null, fn($q, $v) => $q->where('capacity_guests', '>=', $v))
			->when($filters['accept_groups'] ?? null, fn($q) => $q->where('accept_groups', 1))
			->when(!empty($filters['emails_filter']), function ($q) use ($filters) {
				if ($filters['emails_filter'] === 'default_emails') {
					$q->whereHas('contacts', fn($q) => $q->where('default', 1)->whereNotNull('email'));
				} elseif ($filters['emails_filter'] === 'any_emails') {
					$q->whereHas('contacts', fn($q) => $q->whereNotNull('email'));
				} elseif ($filters['emails_filter'] === 'no_emails') {
					$q->whereDoesntHave('contacts', fn($q) => $q->whereNotNull('email'));
				}
			})
			->whereRaw("$haversine BETWEEN ? AND ?", [$minDistance, $maxDistance])
			->count();
	}

	/**
	 * Calculate distance between two points using Haversine formula
	 * 
	 * This method calculates the distance from the city center (kilometer zero) to each hotel.
	 * The city center coordinates are obtained from the cities.latitude and cities.longitude columns.
	 * 
	 * @param float $lat1 Latitude of point 1 (city center)
	 * @param float $lng1 Longitude of point 1 (city center)
	 * @param float $lat2 Latitude of point 2 (hotel)
	 * @param float $lng2 Longitude of point 2 (hotel)
	 * @return float Distance in kilometers
	 */
	private function calculateDistance(float $lat1, float $lng1, float $lat2, float $lng2): float
	{
		// Convert latitude and longitude from degrees to radians
		$lat1 = deg2rad($lat1);
		$lng1 = deg2rad($lng1);
		$lat2 = deg2rad($lat2);
		$lng2 = deg2rad($lng2);

		// Haversine formula
		$dlat = $lat2 - $lat1;
		$dlng = $lng2 - $lng1;
		$a = sin($dlat/2) * sin($dlat/2) + cos($lat1) * cos($lat2) * sin($dlng/2) * sin($dlng/2);
		$c = 2 * atan2(sqrt($a), sqrt(1-$a));

		// Earth's radius in kilometers
		$radius = 6371;

		// Calculate distance
		$distance = $radius * $c;

		return $distance;
	}

	/**
	 * Get the image URL for a hotel
	 * 
	 * This method returns the URL of the hotel's image. It first tries to find the default photo
	 * (where default=1). If no default photo is found, it falls back to the first photo in the collection.
	 * 
	 * @param \App\Models\Hotels\Hotel $hotel
	 * @return string|null
	 */
	private function getHotelImageUrl(\App\Models\Hotels\Hotel $hotel): ?string
	{
		// Try to find the default photo
		$defaultPhoto = $hotel->photos->firstWhere('default', 1);
		if ($defaultPhoto) {
			return asset('storage/' . $defaultPhoto->path_thumb);
		}

		// If no default photo, try to get the first photo
		$firstPhoto = $hotel->photos->first();
		if ($firstPhoto) {
			return asset('storage/' . $firstPhoto->path_thumb);
		}

		// If no photos at all, return null
		return null;
	}

	/**
	 * Get emails from hotel contacts
	 * 
	 * This method extracts emails from the hotel's contacts relationship.
	 * It prioritizes default contacts and returns them as a JSON string.
	 *
	 * @param \App\Models\Hotels\Hotel $hotel
	 * @return string|null
	 */
	private function getDefaultHotelContactEmails(\App\Models\Hotels\Hotel $hotel): ?string
	{
		if ($hotel->contacts->isEmpty()) {
			return null;
		}

		// First check for default contacts
		$defaultContacts = $hotel->contacts->where('default', true);

		if ($defaultContacts->isEmpty()) {

			return null;

		}

		// Return as JSON string
		return json_encode($defaultContacts->pluck('email')->toArray());
	}

	private function computeHaversine(float $lat, float $lng): string
	{
		$haversine = "(6371 * acos(cos(radians($lat)) * cos(radians(latitude)) * cos(radians(longitude) - radians($lng)) + sin(radians($lat)) * sin(radians(latitude))))";

		return $haversine;
	}
}
