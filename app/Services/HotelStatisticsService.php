<?php

namespace App\Services;

use App\Models\Hotels\Hotel;
use Illuminate\Support\Facades\Cache;

class HotelStatisticsService
{
	private int $cacheTime = 720; // Cache time in minutes (720: 60 min x 12 hours)

	public function getTotalHotels(): int
	{
		return Cache::remember('hotel_count', now()->addHour($this->cacheTime), function () {
			return Hotel::count();
		});
	}

	public function getTotalPartnersHotels(): int
	{
		return Cache::remember('hotel_partners_count', now()->addHour($this->cacheTime), function () {
			return Hotel::count();
		});
	}

	public function getTotalVerifiedHotels(): int
	{
		return Cache::remember('hotel_verified_count', now()->addHour($this->cacheTime), function () {
			return Hotel::count();
		});
	}

	public function getTotalPendingHotels(): int
	{
		return Cache::remember('hotel_pending_count', now()->addHour($this->cacheTime), function () {
			return Hotel::count();
		});
	}

	public function forgetHotelStatistics(): void
	{
		Cache::forget('hotel_count');
		Cache::forget('hotel_partners_count');
		Cache::forget('hotel_verified_count');
		Cache::forget('hotel_pending_count');
	}


}
