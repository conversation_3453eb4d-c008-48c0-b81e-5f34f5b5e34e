<?php

namespace App\Services;

use App\Models\City;
use App\Models\Hotel;
use App\Models\Level1Division;
use App\Models\Level2Division;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;

class FilterRegionService
{
	public function filterRegionsGroupByCountry(array $countries = []): Collection
	{
		if (empty($countries)) {
			return collect();
		}

		$regions = Level1Division::with('country') // eager load pentru nume țară
			->when($countries, function ($query) use ($countries) {
				$query->whereIn('country_id', $countries);
			})
			->get();

		return $regions->groupBy('country_id')->map(function ($items) {
			$country = $items->first()->country;

			return [
				'id' => $country->id,
				'country' => $country->name,
				'regions' => $items->map(fn ($region) => [
					'id' => $region->id,
					'name' => $region->name,
				])->values(),
			];
		})->values();
	}

	public function filterSubregionsGroupByRegion(array $regions = []): Collection
	{
		if (empty($regions)) {
			return collect();
		}

		$subregions = Level2Division::with('level1Division') // eager load pentru nume țară
		->when($regions, function ($query) use ($regions) {
			$query->whereIn('level_1_division_id', $regions);
		})
			->get();

		return $subregions->groupBy('level1Division')->map(function ($items) {
			$region = $items->first()->level1Division;

			return [
				'id' => 1, //$region->id,
				'region' => $region->name,
				'subregions' => $items->map(fn ($subregion) => [
					'id' => $subregion->id,
					'name' => $subregion->name,
				])->values(),
			];
		})->values();
	}

	public function filterCitiesGroupBySubregion(array $subregions = []): Collection
	{
		if (empty($subregions)) {
			return collect();
		}

		$cities = City::with('level2Division')
			->when($subregions, function ($query) use ($subregions) {
				$query->whereIn('level_2_division_id', $subregions);
			})
			->get();

		return $cities->groupBy('level2Division')->map(function ($items) {
			$subregion = $items->first()->level2Division;

			return [
				'id'        => 1,
				'subregion' => $subregion->name,
				'cities'    => $items->map(fn($city) => [
					'id'   => $city->id,
					'name' => $city->name . ' (' . $city->level2Division->name . ')',
				])->values(),
			];
		})->values();
	}
}
