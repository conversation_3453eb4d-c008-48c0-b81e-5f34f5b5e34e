<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
	public function up(): void
	{
		Schema::create('outgoing_emails', function (Blueprint $table) {
			$table->id();
			$table->foreignId('hotel_id')->constrained()->onDelete('cascade');
			$table->string('to_email');
			$table->string('subject');
			$table->text('body');
			$table->timestamp('sent_at')->nullable();
			$table->string('graph_message_id')->nullable();
			$table->string('internet_message_id')->nullable()->index();
			$table->text('references')->nullable(); // Adăugăm coloana references
			$table->string('status')->default('pending');
			$table->text('error_message')->nullable();
			$table->timestamps();
		});
	}

    public function down(): void
    {
        Schema::dropIfExists('outgoing_emails');
    }
};