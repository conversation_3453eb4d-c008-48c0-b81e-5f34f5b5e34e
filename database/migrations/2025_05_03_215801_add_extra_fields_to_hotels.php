<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('hotels', function (Blueprint $table) {
            $table->boolean('breakfast')->nullable()->after('hotel_market_id');
            $table->boolean('accept_groups')->nullable()->after('breakfast');
            $table->enum('partner_type', ['vechi', 'nou'])->nullable()->after('accept_groups');
            $table->integer('capacity_rooms')->nullable()->after('partner_type');
            $table->integer('capacity_guests')->nullable()->after('capacity_rooms');
            $table->unsignedTinyInteger('stars')->nullable()->after('capacity_guests')->check('stars between 1 and 5');
            $table->text('notes_prices')->nullable()->after('stars');
            $table->text('notes')->nullable()->after('notes_prices');


            $table->text('website')->nullable();
            $table->text('website_root_url')->nullable();
            $table->string('phone_international')->nullable();
            $table->string('timezone')->nullable();
            $table->string('full_address')->nullable();
            $table->string('borough')->nullable();
            $table->string('street_1')->nullable();
            $table->string('street_2')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('longitude')->nullable();
            $table->string('latitude')->nullable();
            $table->string('link')->nullable();
            $table->string('owner_name')->nullable();
            $table->string('owner_id')->nullable();
            $table->string('place_id')->nullable();
            $table->string('facebook_link')->nullable();
            $table->string('youtube_link')->nullable();
            $table->string('twitter_link')->nullable();
            $table->string('instagram_link')->nullable();
            $table->string('linkedin_link')->nullable();
            $table->string('first_seen_on')->nullable();
            $table->string('reviews_id')->nullable();
            $table->string('reviews_count')->nullable();
            $table->string('reviews_rating')->nullable();
            $table->string('reviews_per_score')->nullable();
            $table->string('is_claimed')->nullable();
            $table->text('characteristics')->nullable();
            $table->text('all_emails')->nullable();
            $table->text('all_contact_pages')->nullable();
            $table->text('all_facebook_links')->nullable();
            $table->text('all_youtube_links')->nullable();
            $table->text('all_twitter_links')->nullable();
            $table->text('all_instagram_links')->nullable();
            $table->text('all_linkedin_links')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('hotels', function (Blueprint $table) {
            $table->dropColumn([
                'breakfast', 'accept_groups', 'partner_type',
                'capacity_rooms', 'capacity_guests', 'stars',
                'notes_prices', 'notes',
                'website', 'website_root_url', 'phone_international', 'timezone', 'full_address',
                'borough', 'street_1', 'street_2', 'postal_code', 'longitude',
                'latitude', 'link', 'owner_name', 'owner_id', 'place_id', 'facebook_link',
                'youtube_link', 'twitter_link', 'instagram_link', 'linkedin_link',
                'first_seen_on', 'reviews_id', 'reviews_count', 'reviews_rating', 'reviews_per_score',
                'is_claimed', 'characteristics', 'all_emails', 'all_contact_pages', 'all_facebook_links',
                'all_youtube_links', 'all_twitter_links', 'all_instagram_links', 'all_linkedin_links'
            ]);
        });
    }
};
