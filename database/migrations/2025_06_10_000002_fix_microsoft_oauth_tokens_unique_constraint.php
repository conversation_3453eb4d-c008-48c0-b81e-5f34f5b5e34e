<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, drop the foreign key constraint and then the unique index
        Schema::table('microsoft_oauth_tokens', function (Blueprint $table) {
            // Drop the foreign key constraint
            $table->dropForeign(['user_id']);

            // Now drop the unique index
            $table->dropUnique(['user_id', 'is_active']);

            // Recreate the foreign key constraint
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });

        // Add a database-specific partial index based on the database driver
        $connection = DB::connection()->getPdo()->getAttribute(\PDO::ATTR_DRIVER_NAME);

        if (in_array($connection, ['pgsql', 'sqlite'])) {
            // PostgreSQL and SQLite support partial indexes
            DB::statement('CREATE UNIQUE INDEX unique_active_token_per_user ON microsoft_oauth_tokens (user_id) WHERE is_active = true');
        } elseif (in_array($connection, ['mysql', 'mariadb'])) {
            // For MySQL/MariaDB, we'll use a simpler approach
            // First, drop any existing index that might conflict
            try {
                DB::statement('ALTER TABLE microsoft_oauth_tokens DROP INDEX unique_active_token_per_user');
            } catch (\Exception $e) {
                // Index doesn't exist, continue
            }

            // MySQL doesn't support partial indexes directly, so we'll use a different approach
            // We'll try to use a generated column if the MySQL version supports it

            try {
                // First, create a generated column that will be NULL when is_active=0
                // This works on MySQL 5.7+ and MariaDB 10.2+
                DB::statement('ALTER TABLE microsoft_oauth_tokens ADD COLUMN active_user_id INT GENERATED ALWAYS AS (IF(is_active=1, user_id, NULL)) STORED');

                // Then create a unique index on this generated column
                // NULL values are not considered in uniqueness constraints, so this effectively
                // creates a partial index that only applies to active tokens
                DB::statement('CREATE UNIQUE INDEX unique_active_token_per_user ON microsoft_oauth_tokens (active_user_id)');
            } catch (\Exception $e) {
                // If generated columns are not supported, fall back to application-level enforcement
                Log::warning('Could not create generated column for Microsoft OAuth tokens. Falling back to application-level enforcement.', [
                    'error' => $e->getMessage()
                ]);

                // The application already deactivates existing tokens before creating new ones,
                // which provides a layer of protection even without database constraints
            }
        } else {
            // For other databases, fall back to application-level enforcement
            // This won't be as robust but will at least not break the migration
            Schema::table('microsoft_oauth_tokens', function (Blueprint $table) {
                // No constraint added - will rely on application logic
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $connection = DB::connection()->getPdo()->getAttribute(\PDO::ATTR_DRIVER_NAME);

        if (in_array($connection, ['pgsql', 'sqlite'])) {
            // Drop the partial index for PostgreSQL and SQLite
            DB::statement('DROP INDEX IF EXISTS unique_active_token_per_user');
        } elseif (in_array($connection, ['mysql', 'mariadb'])) {
            // Drop the index and generated column for MySQL/MariaDB
            try {
                DB::statement('ALTER TABLE microsoft_oauth_tokens DROP INDEX unique_active_token_per_user');
            } catch (\Exception $e) {
                // Index might not exist, continue
            }

            try {
                DB::statement('ALTER TABLE microsoft_oauth_tokens DROP COLUMN active_user_id');
            } catch (\Exception $e) {
                // Column might not exist, continue
            }
        }

        // First drop the foreign key constraint
        Schema::table('microsoft_oauth_tokens', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
        });

        // Recreate the original constraint
        Schema::table('microsoft_oauth_tokens', function (Blueprint $table) {
            $table->unique(['user_id', 'is_active']);

            // Recreate the foreign key constraint
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }
};
