<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hotel_photos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('hotel_id')->constrained('hotels')->onDelete('cascade');
            $table->string('path_img')->unique(); // Calea imaginii principale
            $table->string('path_thumb'); // Calea thumbnail-ului (250px)
            $table->string('path_orig'); // Calea fișierului original descărcat
            $table->boolean('default')->default(false); // Dacă este imaginea principală
            $table->timestamps();

            // Constrângere pentru a evita dublurile de imagini pe același hotel
            $table->unique(['hotel_id', 'path_img']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hotel_photos');
    }
};
