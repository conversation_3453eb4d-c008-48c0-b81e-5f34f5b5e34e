<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('level_2_divisions', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('level_1_division_id')->nullable()->constrained('level_1_divisions')->onDelete('cascade');
            $table->timestamps();

            $table->unique(['name', 'level_1_division_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('level_2_divisions');
    }
};
