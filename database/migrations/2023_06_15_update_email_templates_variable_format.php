<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\EmailTemplate;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get all email templates
        $templates = EmailTemplate::all();
        
        foreach ($templates as $template) {
            // Update content: {{ variable }} -> {{variable}}
            $content = preg_replace('/\{\{\s*([^}]+)\s*\}\}/', '{{$1}}', $template->content);
            
            // Update subject: {{ variable }} -> {{variable}}
            $subject = preg_replace('/\{\{\s*([^}]+)\s*\}\}/', '{{$1}}', $template->subject);
            
            // Save the updated template
            $template->content = $content;
            $template->subject = $subject;
            $template->save();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Get all email templates
        $templates = EmailTemplate::all();
        
        foreach ($templates as $template) {
            // Update content: {{variable}} -> {{ variable }}
            $content = preg_replace('/\{\{([^}]+)\}\}/', '{{ $1 }}', $template->content);
            
            // Update subject: {{variable}} -> {{ variable }}
            $subject = preg_replace('/\{\{([^}]+)\}\}/', '{{ $1 }}', $template->subject);
            
            // Save the updated template
            $template->content = $content;
            $template->subject = $subject;
            $template->save();
        }
    }
};