<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
	public function up(): void
	{
		Schema::table('tasks', function (Blueprint $table) {
			$table->foreignId('team_id')->nullable()->constrained()->cascadeOnDelete();
		});
	}

	public function down(): void
	{
		Schema::table('tasks', function (Blueprint $table) {
			$table->dropForeign(['team_id']);
			$table->dropColumn('team_id');
		});
	}
};
