<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('hotels', function (Blueprint $table) {
            // First drop the unique index on google_id
            $table->dropUnique(['google_id']);

            // Then drop the column
            $table->dropColumn('google_id');
        });

        Schema::table('hotels', function (Blueprint $table) {
            // Add the column back as nullable with unique constraint
            $table->string('google_id')->nullable()->unique()->after('id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('hotels', function (Blueprint $table) {
            // Drop the nullable column
            $table->dropUnique(['google_id']);
            $table->dropColumn('google_id');
        });

        Schema::table('hotels', function (Blueprint $table) {
            // Add it back as non-nullable
            $table->string('google_id')->unique()->after('id');
        });
    }
};
