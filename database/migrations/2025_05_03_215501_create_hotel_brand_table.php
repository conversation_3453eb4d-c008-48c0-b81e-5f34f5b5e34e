<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hotel_brand', function (Blueprint $table) {
            $table->id();
            $table->foreignId('hotel_chain_id')->constrained('hotel_chain')->onDelete('cascade');
            $table->foreignId('hotel_market_id')->nullable()->constrained('hotel_market')->onDelete('cascade');
            $table->string('name');
            $table->string('icon')->nullable();
            $table->timestamps();

            $table->unique(['hotel_chain_id', 'hotel_market_id', 'name']); // Unicitate per lanț și segment de piață
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hotel_brand');
    }
};
