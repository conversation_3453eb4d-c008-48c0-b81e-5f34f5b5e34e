<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hotel_market', function (Blueprint $table) {
            $table->id();
            $table->foreignId('hotel_chain_id')->constrained('hotel_chain')->onDelete('cascade');
            $table->string('name');
            $table->timestamps();

            $table->unique(['hotel_chain_id', 'name']); // Unicitate per lanț hotelier
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hotel_market');
    }
};
