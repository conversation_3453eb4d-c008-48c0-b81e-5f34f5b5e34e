<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\PartnerTypeEnum;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get the enum values from PartnerTypeEnum
        $enumValues = PartnerTypeEnum::values();

        // Drop the existing enum column and recreate it with the new values
        Schema::table('hotels', function (Blueprint $table) {
            $table->dropColumn('partner_type');
        });

        Schema::table('hotels', function (Blueprint $table) use ($enumValues) {
            $table->enum('partner_type', $enumValues)->nullable()->after('accept_groups');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to the original enum values
        Schema::table('hotels', function (Blueprint $table) {
            $table->dropColumn('partner_type');
        });

        Schema::table('hotels', function (Blueprint $table) {
            $table->enum('partner_type', ['vechi', 'nou'])->nullable()->after('accept_groups');
        });
    }
};