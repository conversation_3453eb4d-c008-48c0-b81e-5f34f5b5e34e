<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\HotelPriceRangeEnum;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update any existing records with '-' value to ''
        DB::table('hotels')
            ->where('price_range', '-')
            ->update(['price_range' => '']);

        // Get the enum values from HotelPriceRangeEnum
        $enumValues = HotelPriceRangeEnum::values();

        // Drop the existing enum column and recreate it with the new values
        Schema::table('hotels', function (Blueprint $table) {
            $table->dropColumn('price_range');
        });

        Schema::table('hotels', function (Blueprint $table) use ($enumValues) {
            $table->enum('price_range', $enumValues)->default('')->after('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Define the original enum values
        $originalEnumValues = ['', '-', '$', '$$', '$$$', '$$$$'];

        // Drop the existing enum column and recreate it with the original values
        Schema::table('hotels', function (Blueprint $table) {
            $table->dropColumn('price_range');
        });

        Schema::table('hotels', function (Blueprint $table) use ($originalEnumValues) {
            $table->enum('price_range', $originalEnumValues)->default('')->after('name');
        });
    }
};