<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('actions', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->date('start_date');
            $table->date('end_date');
            $table->string('oras_plecare');
            $table->string('destinatie_city');
            $table->string('destinatie_tara');
            $table->string('destinatei_lat');
            $table->string('destinatie_long');
            $table->integer('max_participanti');
            $table->date('data_finalizare_actiune')->nullable();
            $table->enum('status', ['activ', 'finalizat', 'pending', 'blocat'])->default('activ');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('actions');
    }
};