<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('outgoing_emails', function (Blueprint $table) {
            $table->string('conversation_id')->nullable()->index()->after('internet_message_id');
        });
    }

    public function down(): void
    {
        Schema::table('outgoing_emails', function (Blueprint $table) {
            $table->dropColumn('conversation_id');
        });
    }
};