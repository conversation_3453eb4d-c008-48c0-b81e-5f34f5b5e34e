<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('hotels', function (Blueprint $table) {
            // Adăugăm coloana hotel_brand_id
            $table->foreignId('hotel_brand_id')
                ->nullable()
                ->constrained('hotel_brand')
                ->onDelete('set null')
                ->after('name');

            // Adăugăm coloana hotel_chain_id
            $table->foreignId('hotel_chain_id')
                ->nullable()
                ->constrained('hotel_chain')
                ->onDelete('set null')
                ->after('hotel_brand_id');

            // Adăugăm coloana hotel_market_id
            $table->foreignId('hotel_market_id')
                ->nullable()
                ->constrained('hotel_market')
                ->onDelete('set null')
                ->after('hotel_chain_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('hotels', function (Blueprint $table) {
            $table->dropForeign(['hotel_brand_id']);
            $table->dropForeign(['hotel_chain_id']);
            $table->dropForeign(['hotel_market_id']);

            $table->dropColumn(['hotel_brand_id', 'hotel_chain_id', 'hotel_market_id']);
        });
    }
};
