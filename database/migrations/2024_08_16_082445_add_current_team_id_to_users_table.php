<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
	public function up(): void
	{
		Schema::table('users', function (Blueprint $table) {
			$table->foreignId('current_team_id')
				->nullable()
				->constrained('teams')
				->nullOnDelete();
		});
	}

	public function down(): void
	{
		Schema::table('users', function (Blueprint $table) {
			//
		});
	}
};
