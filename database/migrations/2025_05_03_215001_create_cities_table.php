<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cities', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('country_id')->constrained('countries')->onDelete('cascade');
            $table->foreignId('level_1_division_id')->nullable()->constrained('level_1_divisions')->onDelete('cascade');
            $table->foreignId('level_2_division_id')->nullable()->constrained('level_2_divisions')->onDelete('cascade');
            $table->timestamps();

            $table->unique(['name', 'level_2_division_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cities', function (Blueprint $table) {
            $table->dropForeign(['country_id']);
            $table->dropForeign(['level_1_division_id']);
            $table->dropForeign(['level_2_division_id']);
        });

        Schema::dropIfExists('cities');
    }
};



