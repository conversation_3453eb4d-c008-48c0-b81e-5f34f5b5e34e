<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('hotels', function (Blueprint $table) {
            $table->foreignId('city_id')->nullable()->constrained('cities')->onDelete('set null');
            $table->foreignId('country_id')->nullable()->constrained('countries')->onDelete('set null');
            $table->foreignId('level_1_division_id')->nullable()->constrained('level_1_divisions')->onDelete('set null');
            $table->foreignId('level_2_division_id')->nullable()->constrained('level_2_divisions')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('hotels', function (Blueprint $table) {
            $table->dropForeign(['city_id']);
            $table->dropForeign(['country_id']);
            $table->dropForeign(['level_1_division_id']);
            $table->dropForeign(['level_2_division_id']);
            $table->dropColumn(['city_id', 'country_id', 'level_1_division_id', 'level_2_division_id']);
        });
    }
};
