<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('outgoing_emails', function (Blueprint $table) {
            $table->foreignId('action_id')->nullable()->after('hotel_id')->constrained('actions')->nullOnDelete();
            $table->foreignId('user_id')->nullable()->after('action_id')->constrained('users')->nullOnDelete();
        });
    }

    public function down(): void
    {
        Schema::table('outgoing_emails', function (Blueprint $table) {
            $table->dropForeign(['action_id']);
            $table->dropForeign(['user_id']);
            $table->dropColumn(['action_id', 'user_id']);
        });
    }
};