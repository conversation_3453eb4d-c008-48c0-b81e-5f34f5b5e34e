<?php

namespace Database\Factories\Hotels;

use App\Models\Hotels\HotelChain;
use App\Models\Hotels\HotelMarket;
use Illuminate\Database\Eloquent\Factories\Factory;

class HotelMarketFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = HotelMarket::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'hotel_chain_id' => HotelChain::factory(),
            'name' => $this->faker->unique()->city() . ' Market',
        ];
    }

    /**
     * Indicate that the market belongs to a specific hotel chain.
     *
     * @param  \App\Models\Hotels\HotelChain|int  $chain
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function forChain($chain)
    {
        return $this->state(function (array $attributes) use ($chain) {
            return [
                'hotel_chain_id' => $chain,
            ];
        });
    }
}