<?php

namespace Database\Factories\Hotels;

use App\Models\Hotels\HotelBrand;
use App\Models\Hotels\HotelChain;
use App\Models\Hotels\HotelMarket;
use Illuminate\Database\Eloquent\Factories\Factory;

class HotelBrandFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = HotelBrand::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        // Get a random existing chain or create a new one if none exists
        $chain = HotelChain::inRandomOrder()->first() ?? HotelChain::factory()->create();

        // Get a random market for this chain or create a new one if none exists
        $market = HotelMarket::where('hotel_chain_id', $chain->id)->inRandomOrder()->first() 
            ?? HotelMarket::factory()->forChain($chain->id)->create();

        return [
            'hotel_chain_id' => $chain->id,
            'hotel_market_id' => $market->id,
            'name' => $this->faker->unique()->word() . ' ' . $this->faker->randomElement(['Luxury', 'Comfort', 'Premium', 'Elite', 'Grand', 'Royal']),
            'icon' => null, // No icon by default
        ];
    }

    /**
     * Indicate that the brand belongs to a specific hotel chain and market.
     *
     * @param  \App\Models\Hotels\HotelChain|int  $chain
     * @param  \App\Models\Hotels\HotelMarket|int  $market
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function forChainAndMarket($chain, $market)
    {
        return $this->state(function (array $attributes) use ($chain, $market) {
            return [
                'hotel_chain_id' => $chain,
                'hotel_market_id' => $market,
            ];
        });
    }
}