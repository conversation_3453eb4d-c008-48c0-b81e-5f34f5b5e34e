<?php

namespace Database\Factories\Hotels;

use App\Models\Hotels\HotelChain;
use Illuminate\Database\Eloquent\Factories\Factory;

class HotelChainFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = HotelChain::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => $this->faker->unique()->company() . ' Hotels',
        ];
    }
}