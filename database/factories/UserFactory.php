<?php

namespace Database\Factories;

use App\Enums\RoleEnum;
use App\Models\User;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
	/**
	 * The current password being used by the factory.
	 */
	protected static ?string $password;

	private string $officeDefaultName = 'Sântandrei Team';
	private string $officeName = '';

	/**
	 * Define the model's default state.
	 *
	 * @return array<string, mixed>
	 */
	public function definition(): array
	{
		return [
			'name'              => fake()->name(),
			'email'             => fake()->unique()->safeEmail(),
			'email_verified_at' => now(),
			'password'          => static::$password ??= Hash::make('password'),
			'remember_token'    => Str::random(10),
		];
	}

	/**
	 * Indicate that the model's email address should be unverified.
	 */
	public function unverified(): static
	{
		return $this->state(fn(array $attributes) => [
			'email_verified_at' => null,
		]);
	}

	/**
	 * Attach user to an existing team
	 */
	public function attachToTeam(Team $team, ?RoleEnum $role = null): static
	{
		return $this->afterCreating(function (User $user) use ($team, $role) {
			// Attach user to team without removing existing teams
			$user->teams()->attach($team->id);
			
			// Set team context for role assignment
			setPermissionsTeamId($team->id);
			
			// Assign role if provided
			if ($role) {
				$user->assignRole($role);
			}
		});
	}

	public function masterAdmin(): static
	{
		return $this->afterCreating(function (User $user) {
			// Although Master admin doesn't have a teams
			// We need to create a "Fake" teams
			// Because of spatie/laravel-permission DB structure
			$team = Team::create([
				'name' => 'Master Admin Team',
			]);

//			$user->teams()->attach($team->id, ['role' => RoleEnum::MasterAdmin]);

			$user->update(['current_team_id' => $team->id]);

			setPermissionsTeamId($team->id);

			$user->assignRole(RoleEnum::MasterAdmin);
		});
	}

	public function companyOwner($defaultOfficeName = null): static
	{
		$this->officeName = $defaultOfficeName ?? $this->officeDefaultName;
		return $this->afterCreating(function (User $user) {
			$team = Team::create([
				'name' => $this->officeName,
			]);

			$user->update(['current_team_id' => $team->id]);

			setPermissionsTeamId($team->id);

			$user->assignRole(RoleEnum::CompanyOwner);
		});
	}

	public function agencyAdmin($defaultOfficeName = null): static
	{
		$this->officeName = $defaultOfficeName ?? $this->officeDefaultName;

		return $this->afterCreating(function (User $user) {
			$team = Team::firstOrCreate(['name' => $this->officeName]);

			$user->update(['current_team_id' => $team->id]);

			setPermissionsTeamId($team->id);

			$user->assignRole(RoleEnum::AgencyAdmin);
		});
	}

	public function agent($defaultOfficeName = null): static
	{
		$this->officeName = $defaultOfficeName ?? $this->officeDefaultName;
		return $this->afterCreating(function (User $user) {
			$team = Team::firstOrCreate(['name' => $this->officeName]);

			$user->update(['current_team_id' => $team->id]);

			setPermissionsTeamId($team->id);

			$user->assignRole(RoleEnum::Agent);
		});
	}

	public function partner($defaultOfficeName = null): static
	{
		$this->officeName = $defaultOfficeName ?? $this->officeDefaultName;
		return $this->afterCreating(function (User $user) {
			$team = Team::firstOrCreate(['name' => $this->officeName]);

			$user->update(['current_team_id' => $team->id]);

			setPermissionsTeamId($team->id);

			$user->assignRole(RoleEnum::Partner);
		});
	}

	public function tourist($defaultOfficeName = null): static
	{
		$this->officeName = $defaultOfficeName ?? $this->officeDefaultName;
		return $this->afterCreating(function (User $user) {
			$team = Team::firstOrCreate(['name' => $this->officeName]);

			$user->update(['current_team_id' => $team->id]);

			setPermissionsTeamId($team->id);

			$user->assignRole(RoleEnum::Tourist);
		});
	}

	public function individual($defaultOfficeName = null): static
	{
		$this->officeName = $defaultOfficeName ?? $this->officeDefaultName;
		return $this->afterCreating(function (User $user) {
			$team = Team::firstOrCreate(['name' => $this->officeName]);

			$user->update(['current_team_id' => $team->id]);

			setPermissionsTeamId($team->id);

			$user->assignRole(RoleEnum::Individual);
		});
	}

	public function tutor($defaultOfficeName = null): static
	{
		$this->officeName = $defaultOfficeName ?? $this->officeDefaultName;
		return $this->afterCreating(function (User $user) {
			$team = Team::firstOrCreate(['name' => $this->officeName]);

			$user->update(['current_team_id' => $team->id]);

			setPermissionsTeamId($team->id);

			$user->assignRole(RoleEnum::Tutor);
		});
	}
}
