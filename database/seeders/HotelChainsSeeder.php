<?php

namespace Database\Seeders;

use App\Models\Hotels\HotelChain;
use App\Models\Hotels\HotelMarket;
use App\Models\Hotels\HotelBrand;
use Illuminate\Database\Seeder;

class HotelChainsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        $this->createMarriotChain();
        $this->createAccorChain();
    }
    private function createMarriotChain()
    {
        // Creăm lanțul hotelier Marriott
        $marriottChain = HotelChain::firstOrCreate(['name' => 'Marriott']);

        // Definim segmentele de piață și brandurile aferente
        $markets = [
            'Luxury' => [
                'Edition', 'The Ritz-Carlton', 'The Luxury Collection', 'St. Regis', 'W', 'JW Marriott'
            ],
            'Premium' => [
                'Marriott', 'Sheraton', 'The Marriott Vacation Clubs', 'Delta Hotels',
                'Westin', 'Le Meridien', 'Renaissance', 'Autograph Collection Hotels',
                'Tribute Portfolio', 'Design Hotels', 'Gaylord Hotels', 'MGM Collection'
            ],
            'Select' => [
                'Courtyard', 'Four Points', 'SpringHill Suites', 'Fairfield',
                'AC Hotels', 'Aloft Hotels', 'Moxy Hotels', 'Protea Hotels',
                'City Express by Marriott', 'Four Points Flex'
            ],
            'Longer Stays' => [
                'Residence Inn', 'TownePlace Suites', 'Element',
                'Marriott Executive Apartments', 'Homes & Villas by Marriott Bonvoy',
                'Apartments by Marriott Bonvoy', 'Sonder'
            ]
        ];

        foreach ($markets as $marketName => $brands) {
            // Creăm segmentul de piață
            $market = HotelMarket::firstOrCreate([
                'name' => $marketName,
                'hotel_chain_id' => $marriottChain->id
            ]);

            foreach ($brands as $brand) {
                // Creăm fiecare brand în tabela hotel_brand
                HotelBrand::firstOrCreate([
                    'name' => $brand,
                    'hotel_chain_id' => $marriottChain->id,
                    'hotel_market_id' => $market->id,
                ]);
            }
        }
    }

    private function createAccorChain(): void
    {
        // Creăm lanțul hotelier Accor
        $accorChain = HotelChain::firstOrCreate(['name' => 'Accor']);

        // Definim segmentele de piață, brandurile și URL-urile icoanelor aferente
        $markets = [
            'Luxury' => [
                ['name' => 'Raffles', 'icon' => null],
                ['name' => 'Orient Express', 'icon' => null],
                ['name' => 'Sofitel', 'icon' => null],
                ['name' => 'Banyan Tree', 'icon' => null],
                ['name' => 'Sofitel Legend', 'icon' => null],
                ['name' => 'Fairmont', 'icon' => null],
                ['name' => 'Emblems Collection', 'icon' => null],
                ['name' => 'Faena', 'icon' => null],
                ['name' => 'Gallery M', 'icon' => null],
                ['name' => 'The Purist', 'icon' => null],
            ],
            'Lifestyle by Ennismore' => [
                ['name' => '21c Museum Hotel', 'icon' => null],
                ['name' => '25hours Hotels', 'icon' => null],
                ['name' => 'Delano', 'icon' => null],
                ['name' => 'Hyde', 'icon' => null],
                ['name' => 'Jo&Joe', 'icon' => null],
                ['name' => 'Mama Shelter', 'icon' => null],
                ['name' => 'Mondrian', 'icon' => null],
                ['name' => 'Morgans Originals', 'icon' => null],
                ['name' => 'SLS', 'icon' => null],
                ['name' => 'SO/', 'icon' => null],
                ['name' => 'The Hoxton', 'icon' => null],
            ],
            'Premium' => [
                ['name' => 'Mantis', 'icon' => null],
                ['name' => 'Art Series', 'icon' => null],
                ['name' => 'Pullman', 'icon' => null],
                ['name' => 'Swissôtel', 'icon' => null],
                ['name' => 'Angsana', 'icon' => null],
                ['name' => 'Mövenpick', 'icon' => null],
                ['name' => 'Garrya', 'icon' => null],
                ['name' => 'Grand Mercure', 'icon' => null],
                ['name' => 'Peppers', 'icon' => null],
                ['name' => 'The Sebel', 'icon' => null],
                ['name' => 'Adagio Premium', 'icon' => null],
            ],
            'Midscale' => [
                ['name' => 'Handwritten Collection', 'icon' => null],
                ['name' => 'Mantra', 'icon' => null],
                ['name' => 'Novotel', 'icon' => null],
                ['name' => 'Mercure Hotels', 'icon' => null],
                ['name' => 'Tribe', 'icon' => null],
                ['name' => 'Adagio Original', 'icon' => null],
                ['name' => 'Cassia', 'icon' => null],
                ['name' => 'Folio', 'icon' => null],
                ['name' => 'Dhawa', 'icon' => null],
                ['name' => 'Homm', 'icon' => null],
                ['name' => 'NEQTA', 'icon' => null],
                ['name' => 'Thalassa', 'icon' => null],
            ],
            'Economy' => [
                ['name' => 'BreakFree', 'icon' => null],
                ['name' => 'ibis', 'icon' => null],
                ['name' => 'ibis Styles', 'icon' => null],
                ['name' => 'Adagio Access', 'icon' => null],
                ['name' => 'greet', 'icon' => null],
                ['name' => 'ibis budget','icon' => null],
                ['name' => 'hotelF1', 'icon' => null],
            ],
        ];

        foreach ($markets as $marketName => $brands) {
            // Creăm segmentul de piață
            $market = HotelMarket::firstOrCreate([
                'name' => $marketName,
                'hotel_chain_id' => $accorChain->id
            ]);

            foreach ($brands as $brandData) {
                // Creăm fiecare brand în tabela hotel_brand
                HotelBrand::firstOrCreate([
                    'name' => $brandData['name'],
                    'hotel_chain_id' => $accorChain->id,
                    'hotel_market_id' => $market->id,
                    'icon' => $brandData['icon'],
                ]);
            }
        }
    }
}


