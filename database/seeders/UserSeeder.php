<?php

namespace Database\Seeders;

use App\Enums\RoleEnum;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        User::factory()
            ->masterAdmin()
            ->create([
                'name' => 'Master Admin',
                'email' => '<EMAIL>',
            ]);

        User::factory()
            ->companyOwner()
            ->create([
                'name' => 'Company Owner',
                'email' => '<EMAIL>',
            ]);

        User::factory()
            ->agencyAdmin()
            ->create([
                'name' => 'Agency Admin',
                'email' => '<EMAIL>',
            ]);

        User::factory()
            ->agent()
            ->create([
                'name' => 'Agent User',
                'email' => '<EMAIL>',
            ]);

        User::factory()
            ->partner()
            ->create([
                'name' => 'Regular Partner',
                'email' => '<EMAIL>',
            ]);

        User::factory(5)
            ->tutor()
            ->create();

        User::factory(5)
            ->agent()
            ->create();

        User::factory(5)
            ->partner()
            ->create();



		$offices = ['Brașov Team', 'Timișoara Team', 'Cluj-Napoca Team'];
//		$offices = [];
		foreach ($offices as $index => $office) {
			User::factory()
				->companyOwner($office)
				->create([
					'name' => 'Company Owner '.$office,
					'email' => 'owner'.($index+2).'@company.com',
				]);

			User::factory()
				->agencyAdmin($office)
				->create([
					'name' => 'Agency Admin '.$office,
					'email' => 'admin'.($index+2).'@comapny.com',
				]);

			User::factory()
				->agent($office)
				->create([
					'name' => 'Agent User '.$office,
					'email' => 'agent'.($index+2).'@company.com',
				]);

			User::factory()
				->partner($office)
				->create([
					'name' => 'Regular Partner '.$office,
					'email' => 'partner'.($index+2).'@company.com',
				]);

			User::factory(5)
				->tutor($office)
				->create();

			User::factory()
				->agent($office)
				->create();

			User::factory()
				->partner($office)
				->create();
		}

		// alocam <EMAIL>' la toate celelalte office-uri
	    foreach ($offices as $index => $office) {
			$owner = User::where('email', '<EMAIL>')->first();
			$officeObj = Team::where('name', $office)->first();
			$roleOwner = \Spatie\Permission\Models\Role::where('name', RoleEnum::CompanyOwner)->first();
		    $owner->teams()->attach([$officeObj->id => ['role_id' => $roleOwner->id, 'model_type' => 'App\Models\User']]);

	    }

    }
}
