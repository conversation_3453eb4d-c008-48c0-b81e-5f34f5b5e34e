<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Hotels\HotelContact;

class UpdateHotelContactEmailsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get the count of records before updating
        $count = DB::table('hotel_contact')->count();

        // Use a direct DB update query for better performance with large datasets
        // This approach is much more efficient than loading all records and updating them one by one because:
        // 1. It avoids loading all records into memory
        // 2. It executes a single SQL query instead of N+1 queries
        // 3. It bypasses model events and observers, which can be expensive for large operations
        $updated = DB::update("
            UPDATE hotel_contact 
            SET email = CONCAT('stan.adrian.web+campus.', id, '@gmail.com')
        ");

        $this->command->info("Updated {$updated} hotel contact emails out of {$count} total records.");
        $this->command->info('All hotel contact emails have been updated successfully!');
    }
}
