<?php

namespace Database\Seeders;

use App\Enums\PermissionEnum;
use Illuminate\Database\Seeder;
use App\Enums\RoleEnum as RoleEnum;
use Spatie\Permission\Models\Role;

class RoleAndPermissionSeeder extends Seeder
{
    public function run(): void
    {
        foreach (PermissionEnum::cases() as $permission) {
            \Spatie\Permission\Models\Permission::create(['name' => $permission->value]);
        }

        foreach (RoleEnum::cases() as $role) {
            $role = Role::create(['name' => $role->value]);

            $this->syncPermissionsToRole($role);
        }
    }

    private function syncPermissionsToRole(Role $role): void
    {
        $permissions = [];

        switch ($role->name) {
            case RoleEnum::MasterAdmin->value:
                $permissions = [
                    PermissionEnum::LIST_TEAM,
                    PermissionEnum::CREATE_TEAM,
                    PermissionEnum::EDIT_TEAM,

	                PermissionEnum::LIST_USER,

	                PermissionEnum::CREATE_HOTEL,
	                PermissionEnum::EDIT_HOTEL,
	                PermissionEnum::LIST_HOTEL,
	                PermissionEnum::IMPORT_HOTEL,
                ];
                break;
            case RoleEnum::CompanyOwner->value:
                $permissions = [
                    PermissionEnum::SWITCH_TEAM,

                    PermissionEnum::LIST_USER,
                    PermissionEnum::CREATE_USER,

	                PermissionEnum::CREATE_HOTEL,
	                PermissionEnum::EDIT_HOTEL,
	                PermissionEnum::LIST_HOTEL,
                ];
                break;
            case RoleEnum::AgencyAdmin->value:
                $permissions = [
                    PermissionEnum::LIST_USER,
                    PermissionEnum::CREATE_USER,
                    PermissionEnum::LIST_TASK,
                    PermissionEnum::CREATE_TASK,
                    PermissionEnum::EDIT_TASK,
                    PermissionEnum::DELETE_TASK,
                ];
                break;
            case RoleEnum::Agent->value:
                $permissions = [
                    PermissionEnum::LIST_TASK,
                    PermissionEnum::CREATE_TASK,
                    PermissionEnum::EDIT_TASK,
                    PermissionEnum::DELETE_TASK,
	                PermissionEnum::LIST_HOTEL,
                ];
                break;
            case RoleEnum::Partner->value:
                $permissions = [
	                PermissionEnum::LIST_TASK,
	                PermissionEnum::CREATE_TASK,
	                PermissionEnum::EDIT_TASK,
	                PermissionEnum::DELETE_TASK,
                ];
                break;
            case RoleEnum::Tutor->value:
                $permissions = [
                    PermissionEnum::LIST_TASK,
                ];
                break;
        }

        $role->syncPermissions($permissions);
    }
}
