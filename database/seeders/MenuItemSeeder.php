<?php

namespace Database\Seeders;

use App\Models\MenuItem;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class MenuItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the master-admin role
        $masterAdminRole = Role::where('name', 'master-admin')->first();
        $agencyAdminRole = Role::where('name', 'agency-admin')->first();
        $companyOwnerRole = Role::where('name', 'company-owner')->first();
        $agentRole = Role::where('name', 'agent')->first();
        $partnerRole = Role::where('name', 'partner')->first();

        if (!$masterAdminRole) {
            $this->command->error('Master Admin role not found. Please run the RoleAndPermissionSeeder first.');
            return;
        }

        // Load the menu data from the JSON files
        $verticalMenuJson = file_get_contents(base_path('resources/menu/verticalMenu.json'));
        $verticalMenuData = json_decode($verticalMenuJson);

        $masterAdminMenuJson = file_get_contents(base_path('resources/menu/verticalMenuMasterAdmin.json'));
        $masterAdminMenuData = json_decode($masterAdminMenuJson);

        // Import the master admin menu
        $this->importMenu($masterAdminMenuData->menu, null, [$masterAdminRole, $agencyAdminRole, $companyOwnerRole]);

        // Import the regular menu (for items not in master admin menu)
        $this->importMenu($verticalMenuData->menu, null, [$agentRole, $partnerRole]);

        $this->command->info('Menu items seeded successfully.');
    }

    /**
     * Import menu items from JSON structure.
     *
     * @param array $menuItems
     * @param MenuItem|null $parent
     * @param array $roles
     * @param int $order
     */
    private function importMenu($menuItems, $parent = null, $roles = [], $order = 0)
    {
        foreach ($menuItems as $index => $item) {
            // Check if this is a menu header
            $isHeader = isset($item->menuHeader);

            // Create the menu item
            $menuItem = MenuItem::create([
                'name' => $isHeader ? $item->menuHeader : $item->name,
                'url' => $isHeader ? null : ($item->url ?? null),
                'icon' => $isHeader ? null : ($item->icon ?? null),
                'slug' => $isHeader ? null : ($item->slug ?? null),
                'is_header' => $isHeader,
                'parent_id' => $parent ? $parent->id : null,
                'order' => $order + $index,
                'active' => true,
            ]);

            // Attach roles
            foreach ($roles as $role) {
                if ($role) {
                    $menuItem->roles()->attach($role);
                }
            }

            // Process submenu if exists
            if (!$isHeader && isset($item->submenu) && is_array($item->submenu)) {
                $this->importMenu($item->submenu, $menuItem, $roles);
            }
        }
    }
}