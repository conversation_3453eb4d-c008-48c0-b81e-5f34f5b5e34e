<?php

namespace Database\Seeders;

use App\Models\Hotels\HotelBrand;
use App\Models\Hotels\HotelChain;
use App\Models\Hotels\HotelMarket;
use Illuminate\Database\Seeder;

class HotelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create 5 hotel chains
        HotelChain::factory(5)->create()->each(function ($chain) {
            // For each chain, create 3-5 markets
            $markets = HotelMarket::factory(rand(3, 5))
                ->forChain($chain->id)
                ->create();

            // For each market, create 2-4 brands
            $markets->each(function ($market) use ($chain) {
                HotelBrand::factory(rand(2, 4))
                    ->forChainAndMarket($chain->id, $market->id)
                    ->create();
            });
        });

        $this->command->info('Created 5 hotel chains with markets and brands!');
    }
}