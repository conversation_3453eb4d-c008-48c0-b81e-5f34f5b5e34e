# Vite Build Optimization

## Problem
The development build process using `yarn dev` was taking an excessive amount of time (up to 30 minutes) to compile CSS and JS files. Additionally, changes to files like hotel-map-filters.js were not being detected and reflected in the browser when reloading the page.

## Solution
The build process has been optimized to significantly reduce compilation time in development mode and ensure that file changes are properly detected and reflected in the browser. The following changes were made:

### 1. Separated Entry Points
- Main entry points are always processed (in both development and production)
- Additional files are only processed in production mode

### 2. Enhanced File Watching and HMR
- Configured file watching with polling enabled for reliable change detection
- Set an optimized polling interval (500ms) for faster change detection
- Added explicit file patterns to watch (PHP, JS, CSS, SCSS, Vue, Blade)
- Enhanced HMR configuration with improved timeout and error overlay

### 3. Enabled Caching
- Added cache configuration to speed up subsequent builds

### 4. Optimized Dependencies
- Added specific optimizations for frequently used dependencies (jQuery, <PERSON>trap, Lodash, Moment)

### 5. Implemented Code Splitting
- Added code splitting configuration to create separate chunks for major libraries
- This improves caching and reduces rebuild times

### 6. Added Development-Specific Configuration
- Source maps are only enabled in development mode
- Fewer files are processed in development mode
- Added a watch list for specific files that need HMR in development mode

## How to Use

### Fast Development Mode
Use the new script to run the optimized development build:

```bash
yarn dev:fast
```

This command explicitly sets NODE_ENV to development and uses all the optimizations.

#### File Change Detection
- Changes to files in the watch list will be automatically detected and trigger browser updates
- The system now watches:
  - All JS files in resources/assets/js directory
  - Specific JS files that need special attention (like hotel-map-filters.js)
  - All CSS files in resources/assets/css and resources/css
  - All SCSS files in resources/assets/scss and resources/scss
  - Common file types (.php, .js, .css, .scss, .vue, .blade.php)
- The browser will refresh or update modules without requiring a server restart
- If you need to add more specific files to the watch list, edit the `devWatchFiles` array in vite.config.js
- For file types not covered by default, add them to the `include` array in the `watch` configuration
- For directory patterns, add them to the `refreshPaths` array in the Laravel plugin configuration

### Regular Development Mode
The original command is still available:

```bash
yarn dev
```

### Production Build
For production builds, use:

```bash
yarn build
```

## Additional Recommendations

1. **Enhanced Hot Module Replacement (HMR)**
   - HMR is now properly configured with improved reliability and performance
   - Enhanced configuration includes:
     - Optimized WebSocket protocol settings
     - Increased timeout for better reliability
     - Error overlay for easier debugging
     - Explicit refresh paths for key directories
   - If you need to add more files to the watch list:
     - For specific files: add them to the `devWatchFiles` array
     - For file patterns: add them to the `refreshPaths` array in the Laravel plugin configuration

2. **Optimize Node.js Memory Usage**
   - If you're still experiencing slow builds, try increasing Node.js memory limit:
   ```bash
   NODE_OPTIONS=--max-old-space-size=4096 yarn dev:fast
   ```

3. **Reduce Dependencies**
   - Consider auditing and removing unused dependencies from package.json
   - Use tree-shaking to eliminate unused code

4. **Use Incremental Builds**
   - The current configuration already supports incremental builds through caching

5. **Consider Using Vite's Pre-bundling**
   - Pre-bundling of dependencies is enabled through the optimizeDeps configuration

6. **Monitor Build Performance**
   - Use Vite's built-in profiling: `yarn dev:fast --profile`
