<?php

namespace Tests\Feature;

use App\Enums\RoleEnum;
use App\Models\Team;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;
use Database\Seeders\RoleAndPermissionSeeder;
use Spatie\Permission\Models\Role;

class TeamTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run the seeder
        $this->seed(RoleAndPermissionSeeder::class);
        
        // Create a master admin user
        $this->masterAdmin = User::factory()->create([
            'name' => 'Master Admin',
            'email' => '<EMAIL>',
        ]);

        $team = Team::create([
            'name' => 'Master Admin Team',
        ]);

        $this->masterAdmin->update(['current_team_id' => $team->id]);

        setPermissionsTeamId($team->id);

        // Assign master admin role
        $this->masterAdmin->assignRole(RoleEnum::MasterAdmin->value);
        
        // Create a company owner
        $this->companyOwner = User::factory()->create([
            'name' => 'Clinic Owner',
            'email' => '<EMAIL>',
        ]);

        $teamOwner = Team::create([
            'name' => 'Sântandrei Team',
        ]);

        $this->companyOwner->update(['current_team_id' => $teamOwner->id]);

        setPermissionsTeamId($team->id);
        
        // Assign company owner role
        $this->companyOwner->assignRole(RoleEnum::CompanyOwner->value);
    }

    /** @test */
    public function master_admin_can_view_teams_page()
    {
        $this->actingAs($this->masterAdmin);
        
        $response = $this->get(route('teams.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('content.pages.admin.teams.index');
    }

    /** @test */
    public function company_owner_cannot_view_teams_page()
    {
        $this->actingAs($this->companyOwner);
        
        $response = $this->get(route('teams.index'));
        
        $response->assertStatus(403);
    }

    /** @test */
    public function teams_page_shows_correct_teams()
    {
        $this->actingAs($this->masterAdmin);
        
        // Create some test teams
        $team1 = Team::factory()->create(['name' => 'Team 1']);
        $team2 = Team::factory()->create(['name' => 'Team 2']);
        
        $response = $this->get(route('teams.list') . '?' . http_build_query([
            'draw' => 1,
            'start' => 0,
            'length' => 10,
            'order' => [
                [
                    'column' => 2,
                    'dir' => 'asc'
                ]
            ],
            'search' => [
                'value' => ''
            ]
        ]));
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'draw',
            'recordsTotal',
            'recordsFiltered',
            'data' => [
                '*' => [
                    'id',
                    'fake_id',
                    'name',
                    'user_count',
                    'email',
                    'email_verified_at'
                ]
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertCount(3, $responseData);
        $this->assertEquals('Sântandrei Team', $responseData[0]['name']);
        $this->assertEquals('Team 1', $responseData[1]['name']);
        $this->assertEquals('Team 2', $responseData[2]['name']);
    }

    /** @test */
    public function teams_page_does_not_show_master_admin_team()
    {
        $this->actingAs($this->masterAdmin);

        // Create master admin team
        $masterTeam = Team::factory()->create(['name' => 'Master Admin Team']);

        $response = $this->get(route('teams.list') . '?' . http_build_query([
            'draw' => 1,
            'start' => 0,
            'length' => 10,
            'order' => [
                [
                    'column' => 2,
                    'dir' => 'asc'
                ]
            ],
            'search' => [
                'value' => ''
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'draw',
            'recordsTotal',
            'recordsFiltered',
            'data' => [
                '*' => [
                    'id',
                    'fake_id',
                    'name',
                    'user_count',
                    'email',
                    'email_verified_at'
                ]
            ]
        ]);

        $responseData = $response->json('data');
        foreach ($responseData as $team) {
            $this->assertNotEquals('Master Admin Team', $team['name']);
        }
    }

    /** @test */
    public function teams_page_shows_correct_user_count()
    {
        $this->actingAs($this->masterAdmin);

        // Create a team
        $team = Team::factory()->create(['name' => 'Test Team']);

        // Create and attach users to the team
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        // Get the role ID for Agent
        $roleId = Role::where('name', RoleEnum::Agent->value)->first()->id;
        
        // Attach users to team with role
        $team->users()->attach($user1->id, [
            'model_type' => User::class,
            'role_id' => $roleId
        ]);
        $team->users()->attach($user2->id, [
            'model_type' => User::class,
            'role_id' => $roleId
        ]);

        $response = $this->get(route('teams.list') . '?' . http_build_query([
            'draw' => 1,
            'start' => 0,
            'length' => 10,
            'order' => [
                [
                    'column' => 2,
                    'dir' => 'asc'
                ]
            ],
            'search' => [
                'value' => ''
            ]
        ]));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'draw',
            'recordsTotal',
            'recordsFiltered',
            'data' => [
                '*' => [
                    'id',
                    'fake_id',
                    'name',
                    'user_count',
                    'email',
                    'email_verified_at'
                ]
            ]
        ]);

        $responseData = $response->json('data');
        $testTeam = collect($responseData)->firstWhere('name', 'Test Team');
        $this->assertEquals(2, $testTeam['user_count']);
    }
} 